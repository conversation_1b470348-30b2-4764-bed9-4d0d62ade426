.env
test_docs/
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env.local

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# FastAPI specific
.pytest_cache/
htmlcov/
.coverage
coverage.xml
*.cover

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Temporary files
tmp/
temp/
data/