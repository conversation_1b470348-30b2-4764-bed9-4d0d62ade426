#!/usr/bin/env python
import os
import sys
import asyncio
import logging
import re
import argparse
from dotenv import load_dotenv

import asyncpg

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Parse command line arguments
parser = argparse.ArgumentParser(description="Drop unused tables script")
parser.add_argument(
    "--dry-run",
    action="store_true",
    help="Show what would be done without making changes",
)
parser.add_argument("--force", action="store_true", help="Force drop operations")
args = parser.parse_args()

# Load environment variables
load_dotenv()

# Determine environment
ENV = os.getenv("ENVIRONMENT", "development").lower()
logger.info(f"Running in {ENV} environment")

# Get PostgreSQL URL from environment
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    logger.error("Error: DATABASE_URL environment variable is not set")
    sys.exit(1)

# Handle Heroku-style postgres:// URLs by converting to postgresql://
if DATABASE_URL.startswith("postgres://"):
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)
    logger.info("Converted Heroku postgres:// URL to postgresql:// format")

# Extract connection details for direct asyncpg connection
url_pattern = r"postgres(?:ql)?(?:\+\w+)?:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)"
match = re.match(url_pattern, DATABASE_URL)

if match:
    user, password, host, port, db_name = match.groups()

    # Connection string for asyncpg
    conn_string = f"postgresql://{user}:{password}@{host}:{port}/{db_name}"
else:
    logger.error(f"Could not parse PostgreSQL URL: {DATABASE_URL}")
    sys.exit(1)


async def drop_tables():
    """Drop unused tables from the database."""
    try:
        # Create direct connection for executing SQL
        conn = await asyncpg.connect(conn_string)

        # Tables to drop
        tables_to_drop = ["prompt_details", "response_details"]

        # Check if tables exist
        for table in tables_to_drop:
            exists = await conn.fetchval(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                table,
            )

            if exists:
                logger.info(f"Table '{table}' exists and will be dropped")

                # If not a dry run, drop the table
                if not args.dry_run:
                    if args.force:
                        logger.info(f"Dropping table '{table}'")
                        await conn.execute(f'DROP TABLE "{table}" CASCADE')
                        logger.info(f"Successfully dropped table '{table}'")
                    else:
                        logger.info(f"Would drop table '{table}' (use --force to actually drop)")
            else:
                logger.info(f"Table '{table}' does not exist, nothing to drop")

        # Close the connection
        await conn.close()

        if args.dry_run:
            logger.info("Dry run completed. No changes were made.")
        elif args.force:
            logger.info("Drop operations completed.")
        else:
            logger.info("Run with --force to actually drop the tables.")

    except Exception as e:
        logger.error(f"Error dropping tables: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    logger.info("Starting table cleanup")
    asyncio.run(drop_tables())
    logger.info("Table cleanup complete")
