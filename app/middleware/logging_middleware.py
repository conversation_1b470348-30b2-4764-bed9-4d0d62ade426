import logging
import time
import traceback
from typing import Dict, Optional, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTP<PERSON>iddleware, RequestResponseEndpoint

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging request and response information.

    Logs:
    - Request path, method and processing time for all requests
    - Additional details for errors (status >= 500)
    - Optionally excludes specified paths from logging
    """

    def __init__(
        self,
        app: FastAPI,
        exclude_paths: Optional[Dict[str, bool]] = None,
    ):
        """
        Initialize the middleware.

        Args:
            app: The FastAPI application
            exclude_paths: Dictionary of paths to exclude from logging
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or {}

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        Process the request and log information about it.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler
        """
        # Skip logging for excluded paths
        if request.url.path in self.exclude_paths:
            return await call_next(request)

        start_time = time.time()

        # Extract useful request info for logging
        request_id = request.headers.get("X-Request-ID", "")
        user_id = None
        content_length = request.headers.get("Content-Length", "")
        content_type = request.headers.get("Content-Type", "")
        auth_header = request.headers.get("Authorization", "")
        # Mask token content for security
        has_auth = "Yes" if auth_header else "No"

        # Try to extract user_id from various sources
        try:
            if request.query_params.get("user_id"):
                user_id = request.query_params.get("user_id")
            elif request.method in ("POST", "PUT", "PATCH"):
                if "application/json" in content_type:
                    body = await request.json()
                    user_id = body.get("user_id")
        except Exception as e:
            # Log errors in extracting user_id
            logger.warning(f"Error extracting user_id: {str(e)}")
            pass

        # Default log details
        log_details: Dict[str, Any] = {
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else None,
            "request_id": request_id,
            "user_id": user_id,
            "content_type": content_type,
            "content_length": content_length,
            "has_auth_header": has_auth,
            "host": request.headers.get("Host", ""),
            "user_agent": request.headers.get("User-Agent", ""),
        }

        logger.debug(f"Processing request: {log_details}")

        try:
            # Process the request and measure timing
            response = await call_next(request)
            process_time = time.time() - start_time

            # Add response info to log details
            log_details["status_code"] = response.status_code
            log_details["process_time_ms"] = round(process_time * 1000, 2)
            log_details["response_headers"] = dict(response.headers)

            # Log details for successful or client error responses
            if response.status_code < 400:
                logger.info(
                    f"Request: {request.method} {request.url.path} - "
                    f"Status: {response.status_code} - "
                    f"Time: {log_details['process_time_ms']}ms"
                )
            elif response.status_code < 500:
                # For 4xx errors, log more details
                logger.warning(f"Client error - Status: {response.status_code}", extra=log_details)
            else:
                # For 5xx errors, log more details
                logger.error(f"Server error - Status: {response.status_code}", extra=log_details)

            return response

        except Exception as exc:
            # Log exceptions with stack trace
            process_time = time.time() - start_time
            log_details["process_time_ms"] = round(process_time * 1000, 2)
            log_details["exception"] = str(exc)
            log_details["traceback"] = traceback.format_exc()

            logger.exception(
                f"Unhandled exception processing request: {request.method} {request.url.path}",
                extra=log_details,
            )

            # Reraise the exception for FastAPI's exception handlers
            raise
