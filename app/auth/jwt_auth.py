import os
import logging
from typing import Dict, cast

import jwt
from fastapi import HTTPEx<PERSON>, status
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError, PyJWTError

# Configure logging
logger = logging.getLogger(__name__)

# JWT Configuration - load from environment variables
# In production, this should be set to a secure random value in the environment
# For CI/testing, a fallback value is used in GitHub Actions workflow
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

# Fail early if the secret key is not set
if not JWT_SECRET_KEY:
    logger.critical("JWT_SECRET_KEY environment variable is not set")
    raise ValueError("JWT_SECRET_KEY environment variable must be set")
else:
    logger.info(f"JWT auth configured with algorithm: {JWT_ALGORITHM}")


def verify_token(token: str) -> Dict:
    """
    Verify a JWT token and return its payload if valid.

    Args:
        token: The JWT token to verify

    Returns:
        The decoded token payload

    Raises:
        HTTPException: If the token is invalid or expired
    """
    if not token:
        logger.error("Empty token provided to verify_token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token is missing",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Log token length and format for debugging (without revealing token contents)
    token_length = len(token)
    token_format_valid = token.count(".") == 2
    logger.debug(f"Verifying token: length={token_length}, format_valid={token_format_valid}")

    try:
        # Decode and verify the token
        # We've already checked that JWT_SECRET_KEY is not None
        secret_key = cast(str, JWT_SECRET_KEY)
        decoded = jwt.decode(token, secret_key, algorithms=[JWT_ALGORITHM])

        # Log successful verification (don't log the full payload for security)
        if "sub" in decoded:
            logger.info(f"Token verified successfully for subject: {decoded.get('sub')}")
        else:
            logger.info("Token verified successfully (no subject claim)")

        return decoded

    except ExpiredSignatureError as e:
        logger.warning(f"Token has expired: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except (PyJWTError, InvalidTokenError) as e:
        # Log detailed error info but return a generic message to the client
        logger.error(f"JWT token validation error: {str(e)}, token_length={token_length}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except ValueError as e:
        logger.error(f"Value error during token validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        # Catch any other unexpected errors
        logger.exception(f"Unexpected error verifying token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Server error during authentication",
            headers={"WWW-Authenticate": "Bearer"},
        )
