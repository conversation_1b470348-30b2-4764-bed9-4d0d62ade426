from fastapi import Depends
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.auth.jwt_auth import verify_token

# Security scheme for JWT bearer tokens with automatic error handling
security = HTTPBearer(auto_error=True)


async def get_current_authenticated_request(
    credentials: HTTPAuthorizationCredentials = Depends(security),
):
    """
    Authenticate the request by verifying the JWT token in the Authorization header.

    This dependency can be used in any endpoint that requires authentication.

    Args:
        credentials: The HTTP Authorization credentials containing the bearer token

    Returns:
        The decoded token payload if authentication is successful

    Raises:
        HTTPException: If the token is invalid or missing
    """
    # The credentials are automatically validated by the HTTPBearer dependency
    # We just need to verify the token itself
    return verify_token(credentials.credentials)
