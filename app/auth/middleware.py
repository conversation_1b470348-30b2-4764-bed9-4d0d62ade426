from typing import List, Optional, cast
import logging

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import RequestResponseEndpoint
from fastapi import HTTPException

from app.auth.jwt_auth import verify_token

logger = logging.getLogger(__name__)


class JWTAuthMiddleware:
    """
    Middleware for JWT Authentication.

    This middleware applies JWT authentication to all routes except for those in exclude_paths.
    """

    def __init__(
        self,
        app,
        exclude_paths: Optional[List[str]] = None,
        auth_header_name: str = "Authorization",
    ):
        """
        Initialize the middleware.

        Args:
            app: The FastAPI application
            exclude_paths: List of paths to exclude from authentication (e.g., ["/health"])
            auth_header_name: The name of the authorization header (default: "Authorization")
        """
        self.app = app
        self.exclude_paths = exclude_paths or []
        self.auth_header_name = auth_header_name

        # Cache compiled exclusion paths for faster lookups
        self._exclusion_prefixes = set(self.exclude_paths)
        logger.info(f"JWT Auth Middleware initialized with excluded paths: {self.exclude_paths}")

    async def __call__(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        Process the request and apply JWT authentication.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler
        """
        path = request.url.path
        method = request.method
        client_ip = request.client.host if request.client else "unknown"

        # Check if path is excluded from authentication
        if self._should_skip_auth(path):
            logger.debug(f"Skipping auth for excluded path: {path}")
            return await call_next(request)

        logger.debug(f"Authenticating request: {method} {path} from {client_ip}")

        # Get and validate Authorization header
        auth_header = request.headers.get(self.auth_header_name)
        if not auth_header:
            logger.warning(
                f"Authentication required but no Authorization header found: {method} {path} from {client_ip}"
            )
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authentication required"},
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract token from header
        try:
            scheme, token = auth_header.split()
            if scheme.lower() != "bearer":
                logger.warning(f"Invalid authentication scheme: {scheme} for {method} {path} from {client_ip}")
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid authentication scheme"},
                    headers={"WWW-Authenticate": "Bearer"},
                )
        except ValueError:
            logger.warning(f"Invalid authorization header format for {method} {path} from {client_ip}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid authorization header format"},
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify token
        try:
            verify_token(token)
        except HTTPException as e:
            logger.error(f"Token verification failed for {method} {path} from {client_ip}: {str(e)}")
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail},
                headers=e.headers,
            )

        # If token is valid, proceed to the next handler
        try:
            response = await call_next(request)
            return cast(Response, response)
        except Exception as e:
            logger.error(f"Error in downstream handler for {method} {path} from {client_ip}: {str(e)}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": str(e)},
                headers={"WWW-Authenticate": "Bearer"},
            )

    def _should_skip_auth(self, path: str) -> bool:
        """
        Check if authentication should be skipped for this path.

        Args:
            path: The request path

        Returns:
            True if authentication should be skipped, False otherwise
        """
        # Check for exact matches first
        if path in self._exclusion_prefixes:
            return True

        # Then check for proper path prefixes (must end with / or be a complete path segment)
        return any(
            path == prefix
            or (path.startswith(prefix) and (path[len(prefix)] == "/" if len(path) > len(prefix) else True))
            for prefix in self._exclusion_prefixes
        )
