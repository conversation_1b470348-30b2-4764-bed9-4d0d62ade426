import os
import logging
from typing import Optional

from app.services.llm_chain_factory import LLMChainFactory
from app.services.llm_trace_service import LLMTraceService

logger = logging.getLogger(__name__)

# Global service instances
_trace_service: Optional[LLMTraceService] = None
_llm_chain_factory: Optional[LLMChainFactory] = None


async def get_llm_chain_factory() -> LLMChainFactory:
    """Get the LLM chain factory instance."""
    global _llm_chain_factory, _trace_service

    if _llm_chain_factory is None:
        # Use DATABASE_URL (Heroku convention) with fallback to POSTGRES_URL (local development)
        postgres_url = os.getenv("DATABASE_URL") or os.getenv("POSTGRES_URL")

        # Initialize trace service if not already initialized
        if postgres_url and _trace_service is None:
            try:
                logger.info("Initializing LLM trace service")
                _trace_service = LLMTraceService(postgres_url=postgres_url)
                await _trace_service.initialize()
                logger.info("LLM trace service initialized")
            except Exception as e:
                logger.error(f"Failed to initialize LLM tracing: {str(e)}")
                _trace_service = None

        # Create chain factory
        _llm_chain_factory = LLMChainFactory(
            trace_service=_trace_service,
            postgres_url=postgres_url,
        )

        if not postgres_url:
            logger.warning("No database URL found, LLM chain factory initialized without database connection")

        # Initialize if needed
        await _llm_chain_factory.initialize()

    return _llm_chain_factory
