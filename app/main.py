import logging
import os

from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from starlette.middleware.base import BaseHTTPMiddleware

from app.api import document, lawsuit, query
from app.auth.middleware import JWTAuthMiddleware
from app.middleware.logging_middleware import RequestLoggingMiddleware
from app.utils.dependencies import get_llm_chain_factory

# Load environment variables from .env file if present
load_dotenv()

# Configure logging with appropriate level from environment
log_level = os.getenv("LOG_LEVEL", "INFO")
logging.basicConfig(
    level=getattr(logging, log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create the FastAPI app with documentation
app = FastAPI(
    title="SoloCircuit API",
    description="API for document ingestion, semantic search, and structured data extraction",
    version="0.1.0",
    openapi_tags=[
        {"name": "Document Management", "description": "Operations with documents"},
        {"name": "Query", "description": "Query operations"},
        {"name": "Lawsuit Data", "description": "Lawsuit data operations"},
    ],
)


# Startup event to initialize services
@app.on_event("startup")
async def startup_event():
    """Initialize services on app startup."""
    # Initialize the LLM chain factory
    await get_llm_chain_factory()


# Add request logging middleware first (to log all requests, including those handled by other middleware)
logger.info("Adding request logging middleware")
app.add_middleware(
    RequestLoggingMiddleware,
    exclude_paths={
        "/health": True,  # Don't log health checks
        "/docs": True,  # Don't log docs requests
        "/redoc": True,  # Don't log redoc requests
        "/openapi.json": True,  # Don't log OpenAPI schema requests
    },
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Configure OpenAPI security scheme
app.swagger_ui_init_oauth = {
    "usePkceWithAuthorizationCodeGrant": True,
    "useBasicAuthenticationWithAccessCodeGrant": True,
}

# Additional Swagger UI settings
app.swagger_ui_parameters = {
    "persistAuthorization": True,
    "tryItOutEnabled": True,
    "displayRequestDuration": True,
    "filter": True,
}


# HTTP Keepalive middleware to ensure connection reuse
class HTTPKeepaliveMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        # Add keepalive headers to response
        response.headers["Connection"] = "keep-alive"
        response.headers["Keep-Alive"] = "timeout=65, max=100"
        return response


# Add keepalive middleware
app.add_middleware(HTTPKeepaliveMiddleware)


# Define a function to customize the OpenAPI schema
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Add JWT Bearer security scheme
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter your JWT token",
        }
    }

    # Add global security requirement to all operations
    openapi_schema["security"] = [{"Bearer": []}]

    # Ensure each path operation also has security defined
    for path in openapi_schema["paths"].values():
        for operation in path.values():
            if isinstance(operation, dict):
                operation["security"] = [{"Bearer": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


# Set the custom OpenAPI schema function
# Use proper assignment for FastAPI openapi
app.openapi = custom_openapi  # type: ignore

# Paths that don't require authentication
# These endpoints will be accessible without a JWT token
AUTH_EXCLUDE_PATHS = [
    # Public API documentation
    "/docs",
    "/redoc",
    "/openapi.json",
    # Public endpoints
    "/health",
    "/",
]

# Check if authentication should be skipped (for development/testing)
SKIP_AUTH = os.getenv("SKIP_AUTH", "").lower() == "true"

# Add JWT Authentication middleware if not skipping auth
if not SKIP_AUTH:
    app.add_middleware(
        BaseHTTPMiddleware,
        dispatch=JWTAuthMiddleware(app, exclude_paths=AUTH_EXCLUDE_PATHS),
    )
    logger.info("JWT Authentication middleware enabled")
else:
    logger.warning("JWT Authentication is DISABLED. " "This should only be used for development/testing!")

# Include API routers with appropriate prefixes and tags
app.include_router(document.router, prefix="/api", tags=["Document Management"])
app.include_router(query.router, prefix="/api", tags=["Query"])
app.include_router(lawsuit.router, prefix="/api", tags=["Lawsuit Data"])


@app.get("/")
async def root():
    """Root endpoint that returns basic API info."""
    return {
        "name": "SoloCircuit API",
        "version": "0.1.0",
        "description": ("API for document ingestion, semantic search, " "and structured data extraction"),
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring and load balancers."""
    return {"status": "healthy"}
