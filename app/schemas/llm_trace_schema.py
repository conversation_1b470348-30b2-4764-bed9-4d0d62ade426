from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field


class TraceStatus(str, Enum):
    """Status of an LLM trace."""

    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"


class LLMTraceCreate(BaseModel):
    """Schema for creating a new LLM trace."""

    user_id: Optional[str] = Field(None, description="The ID of the user who made the request")
    session_id: Optional[str] = Field(None, description="Session ID for grouping related traces")
    request_id: Optional[str] = Field(None, description="Unique ID for the request")
    provider: str = Field(..., description="The LLM provider (e.g., OpenAI, Google)")
    model: str = Field(..., description="The model used (e.g., gpt-4, gemini-pro)")
    prompt: Union[str, List[Dict[str, Any]]] = Field(..., description="The prompt sent to the LLM")
    prompt_tokens: Optional[int] = Field(None, description="Number of tokens in the prompt")
    response: Optional[Union[str, Dict[str, Any]]] = Field(None, description="The raw response from the LLM")
    completion_tokens: Optional[int] = Field(None, description="Number of tokens in the completion")
    total_tokens: Optional[int] = Field(None, description="Total number of tokens used")
    status: TraceStatus = Field(TraceStatus.SUCCESS, description="Status of the trace")
    error_message: Optional[str] = Field(None, description="Error message if status is ERROR")
    duration_ms: Optional[int] = Field(None, description="Duration of the request in milliseconds")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Model parameters used (temperature, etc.)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata about the request")
    tags: Optional[List[str]] = Field(None, description="Tags for categorizing the trace")


class LLMTrace(LLMTraceCreate):
    """Schema for an LLM trace with database ID and timestamps."""

    id: str = Field(..., description="Unique identifier for the trace")
    created_at: datetime = Field(..., description="When the trace was created")
    updated_at: Optional[datetime] = Field(None, description="When the trace was last updated")

    class Config:
        orm_mode = True


class LLMTraceBatch(BaseModel):
    """Schema for batch creation of LLM traces."""

    traces: List[LLMTraceCreate] = Field(..., description="List of traces to create")


class LLMTraceFilter(BaseModel):
    """Schema for filtering LLM traces."""

    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    provider: Optional[str] = None
    model: Optional[str] = None
    status: Optional[TraceStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    tags: Optional[List[str]] = None
