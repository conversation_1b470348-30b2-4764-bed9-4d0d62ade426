from enum import Enum
from typing import Dict, List, Optional, Tuple

from pydantic import BaseModel, Field


class LLMProvider(str, Enum):
    """LLM providers supported by the system"""

    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROK = "grok"


# Provider mapping for ModelName - initialized before the class definition
_provider_map: Dict[str, "LLMProvider"] = {}


class ModelName(str, Enum):
    """Available LLM models organized by provider"""

    # OpenAI models
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_35_TURBO_FLASH = "gpt-3.5-turbo-flash"
    GPT_41 = "gpt-4.1"
    GPT_41_MINI = "gpt-4.1-mini"
    GPT_41_NANO = "gpt-4.1-nano"
    GPT_45_PREVIEW = "gpt-4.5-preview"
    GPT_4O_REALTIME_PREVIEW = "gpt-4o-realtime-preview"
    O1 = "o1"
    O1_PRO = "o1-pro"
    O3 = "o3"
    O4_MINI = "o4-mini"
    O3_MINI = "o3-mini"
    O1_MINI = "o1-mini"

    # Anthropic models - direct API only (no AWS/GCP)
    CLAUDE_3_7_SONNET_LATEST = "claude-3-7-sonnet-latest"
    CLAUDE_3_5_HAIKU_LATEST = "claude-3-5-haiku-latest"
    CLAUDE_3_5_SONNET_LATEST = "claude-3-5-sonnet-latest"
    CLAUDE_3_OPUS_LATEST = "claude-3-opus-latest"

    # Google models
    GEMINI_25_FLASH_PREVIEW = "gemini-2.5-flash-preview-04-17"
    GEMINI_25_PRO_PREVIEW = "gemini-2.5-pro-exp-03-25"
    GEMINI_20_FLASH = "gemini-2.0-flash"
    GEMINI_20_FLASH_LITE = "gemini-2.0-flash-lite"

    # Grok models
    GROK_3_BETA = "grok-3-beta"
    GROK_3 = "grok-3"
    GROK_3_LATEST = "grok-3-latest"
    GROK_3_FAST_BETA = "grok-3-fast-beta"
    GROK_3_FAST = "grok-3-fast"
    GROK_3_FAST_LATEST = "grok-3-fast-latest"
    GROK_3_MINI_BETA = "grok-3-mini-beta"
    GROK_3_MINI = "grok-3-mini"
    GROK_3_MINI_LATEST = "grok-3-mini-latest"
    GROK_3_MINI_FAST_BETA = "grok-3-mini-fast-beta"
    GROK_3_MINI_FAST = "grok-3-mini-fast"
    GROK_3_MINI_FAST_LATEST = "grok-3-mini-fast-latest"
    GROK_2_VISION_1212 = "grok-2-vision-1212"
    GROK_2_VISION = "grok-2-vision"
    GROK_2_VISION_LATEST = "grok-2-vision-latest"
    GROK_2_IMAGE_1212 = "grok-2-image-1212"
    GROK_2_IMAGE = "grok-2-image"
    GROK_2_IMAGE_LATEST = "grok-2-image-latest"

    @property
    def provider(self) -> Optional[LLMProvider]:
        """Get the provider for this model"""
        return _provider_map.get(self)


# Initialize the model provider mapping after ModelName is defined
_provider_map.update(
    {
        # OpenAI models
        ModelName.GPT_4O: LLMProvider.OPENAI,
        ModelName.GPT_4O_MINI: LLMProvider.OPENAI,
        ModelName.GPT_35_TURBO_FLASH: LLMProvider.OPENAI,
        ModelName.GPT_41: LLMProvider.OPENAI,
        ModelName.GPT_41_MINI: LLMProvider.OPENAI,
        ModelName.GPT_41_NANO: LLMProvider.OPENAI,
        ModelName.GPT_45_PREVIEW: LLMProvider.OPENAI,
        ModelName.GPT_4O_REALTIME_PREVIEW: LLMProvider.OPENAI,
        ModelName.O1: LLMProvider.OPENAI,
        ModelName.O1_PRO: LLMProvider.OPENAI,
        ModelName.O3: LLMProvider.OPENAI,
        ModelName.O4_MINI: LLMProvider.OPENAI,
        ModelName.O3_MINI: LLMProvider.OPENAI,
        ModelName.O1_MINI: LLMProvider.OPENAI,
        # Anthropic models - direct API only
        ModelName.CLAUDE_3_7_SONNET_LATEST: LLMProvider.ANTHROPIC,
        ModelName.CLAUDE_3_5_HAIKU_LATEST: LLMProvider.ANTHROPIC,
        ModelName.CLAUDE_3_5_SONNET_LATEST: LLMProvider.ANTHROPIC,
        ModelName.CLAUDE_3_OPUS_LATEST: LLMProvider.ANTHROPIC,
        # Google models
        ModelName.GEMINI_25_FLASH_PREVIEW: LLMProvider.GOOGLE,
        ModelName.GEMINI_25_PRO_PREVIEW: LLMProvider.GOOGLE,
        ModelName.GEMINI_20_FLASH: LLMProvider.GOOGLE,
        ModelName.GEMINI_20_FLASH_LITE: LLMProvider.GOOGLE,
        # Grok models
        ModelName.GROK_3_BETA: LLMProvider.GROK,
        ModelName.GROK_3: LLMProvider.GROK,
        ModelName.GROK_3_LATEST: LLMProvider.GROK,
        ModelName.GROK_3_FAST_BETA: LLMProvider.GROK,
        ModelName.GROK_3_FAST: LLMProvider.GROK,
        ModelName.GROK_3_FAST_LATEST: LLMProvider.GROK,
        ModelName.GROK_3_MINI_BETA: LLMProvider.GROK,
        ModelName.GROK_3_MINI: LLMProvider.GROK,
        ModelName.GROK_3_MINI_LATEST: LLMProvider.GROK,
        ModelName.GROK_3_MINI_FAST_BETA: LLMProvider.GROK,
        ModelName.GROK_3_MINI_FAST: LLMProvider.GROK,
        ModelName.GROK_3_MINI_FAST_LATEST: LLMProvider.GROK,
        ModelName.GROK_2_VISION_1212: LLMProvider.GROK,
        ModelName.GROK_2_VISION: LLMProvider.GROK,
        ModelName.GROK_2_VISION_LATEST: LLMProvider.GROK,
        ModelName.GROK_2_IMAGE_1212: LLMProvider.GROK,
        ModelName.GROK_2_IMAGE: LLMProvider.GROK,
        ModelName.GROK_2_IMAGE_LATEST: LLMProvider.GROK,
    }
)


class LLMTask(str, Enum):
    """Tasks that can be performed using LLMs"""

    QUESTION_ANSWERING = "question_answering"
    LAWSUIT_EXTRACTION = "lawsuit_extraction"
    DOCUMENT_EXTRACTION = "document_extraction"


class ModelConfig(BaseModel):
    """Configuration for a specific LLM model"""

    provider: LLMProvider
    temperature: float = Field(0.0, description="Temperature setting for the model (0.0-1.0)")
    max_tokens: Optional[int] = Field(None, description="Maximum number of tokens for the response")
    additional_params: Dict = Field(default_factory=dict, description="Additional provider-specific parameters")


class ProviderConfig(BaseModel):
    """Configuration for a specific LLM provider"""

    api_key_env: str = Field(..., description="Environment variable name for the API key")
    default_model: ModelName = Field(..., description="Default model to use for this provider")


class TaskConfig(BaseModel):
    """Configuration for a specific NLP task"""

    default_provider_model: Tuple[LLMProvider, ModelName] = Field(
        ...,
        description="Default provider and model to use for this task as a tuple (provider, model)",
    )
    allowed_providers: List[LLMProvider] = Field(..., description="List of allowed providers for this task")
    chunk_size: int = Field(1000, description="Default chunk size for document splitting")
    chunk_overlap: int = Field(200, description="Default chunk overlap for document splitting")
    default_k: int = Field(4, description="Default number of chunks to retrieve")


class LLMConfig(BaseModel):
    """Overall LLM configuration"""

    providers: Dict[LLMProvider, ProviderConfig] = Field(..., description="Provider configurations")
    models: Dict[str, ModelConfig] = Field(..., description="Model configurations by model key (provider:model_name)")
    tasks: Dict[LLMTask, TaskConfig] = Field(..., description="Task configurations by task type")
    global_default: Tuple[LLMProvider, ModelName] = Field(..., description="Global default provider and model")


# Helper functions for easy access to models by provider
def get_models_for_provider(provider: LLMProvider) -> List[ModelName]:
    """Get all models for a specific provider"""
    return [model for model in ModelName if model.provider == provider]


# Map to easily look up available models for each provider
PROVIDER_MODELS = {provider: get_models_for_provider(provider) for provider in LLMProvider}


def get_model_key(provider: LLMProvider, model: ModelName) -> str:
    """Create a model key from provider and model"""
    return f"{provider}: {model}"
