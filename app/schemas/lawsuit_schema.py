from enum import Enum
from typing import Dict, Any, Optional

from pydantic import BaseModel, EmailStr, Field


class USState(str, Enum):
    ALABAMA = "Alabama"
    ALASKA = "Alaska"
    AMERICAN_SAMOA = "American Samoa"
    ARIZONA = "Arizona"
    ARKANSAS = "Arkansas"
    CALIFORNIA = "California"
    COLORADO = "Colorado"
    CONNECTICUT = "Connecticut"
    DELAWARE = "Delaware"
    DISTRICT_OF_COLUMBIA = "District of Columbia"
    FLORIDA = "Florida"
    GEORGIA = "Georgia"
    GUAM = "Guam"
    HAWAII = "Hawaii"
    IDAHO = "Idaho"
    ILLINOIS = "Illinois"
    INDIANA = "Indiana"
    IOWA = "Iowa"
    KANSAS = "Kansas"
    KENTUCKY = "Kentucky"
    LOUISIANA = "Louisiana"
    MAINE = "Maine"
    MARYLAND = "Maryland"
    MASSACHUSETTS = "Massachusetts"
    MICHIGAN = "Michigan"
    MINNESOTA = "Minnesota"
    MISSISSIPPI = "Mississippi"
    MISSOURI = "Missouri"
    MONTANA = "Montana"
    NEBRASKA = "Nebraska"
    NEVADA = "Nevada"
    NEW_HAMPSHIRE = "New Hampshire"
    NEW_JERSEY = "New Jersey"
    NEW_MEXICO = "New Mexico"
    NEW_YORK = "New York"
    NORTH_CAROLINA = "North Carolina"
    NORTH_DAKOTA = "North Dakota"
    NORTHERN_MARIANA_ISLANDS = "Northern Mariana Islands"
    OHIO = "Ohio"
    OKLAHOMA = "Oklahoma"
    OREGON = "Oregon"
    PENNSYLVANIA = "Pennsylvania"
    PUERTO_RICO = "Puerto Rico"
    RHODE_ISLAND = "Rhode Island"
    SOUTH_CAROLINA = "South Carolina"
    SOUTH_DAKOTA = "South Dakota"
    TENNESSEE = "Tennessee"
    TEXAS = "Texas"
    UNITED_STATES_MINOR_OUTLYING_ISLANDS = "United States Minor Outlying Islands"
    UTAH = "Utah"
    VERMONT = "Vermont"
    VIRGIN_ISLANDS = "Virgin Islands"
    VIRGINIA = "Virginia"
    WASHINGTON = "Washington"
    WEST_VIRGINIA = "West Virginia"
    WISCONSIN = "Wisconsin"
    WYOMING = "Wyoming"


class MailingAddress(BaseModel):
    customer_street: Optional[str] = Field(None, description="Street address of the customer")
    customer_city: Optional[str] = Field(None, description="City of the customer's address")
    customer_state: Optional[str] = Field(None, description="State of the customer's address")
    customer_zip_code: Optional[str] = Field(None, description="ZIP code of the customer's address")


class Customer(BaseModel):
    name: Optional[str] = Field(None, description="Full name of the customer (typically the defendant)")
    phone_number: Optional[str] = Field(None, description="Contact phone number for the customer")
    mailing_address: Optional[MailingAddress] = Field(None, description="Customer's mailing address information")


class Organization(BaseModel):
    id: Optional[int] = Field(None, description="Unique identifier for the organization")
    name: Optional[str] = Field(None, description="Name of the organization")


class LawsuitData(BaseModel):
    is_prelit: Optional[bool] = Field(
        None,
        description="Whether the case is pre-litigation (true) or already filed as a lawsuit (false)",
    )
    lawsuit_exists: Optional[bool] = Field(None, description="Whether a lawsuit exists")
    lawsuit_state: Optional[USState] = Field(
        None,
        description="State where the lawsuit is filed (must be one of the 50 U.S. states, DC, or a US territory)",
    )
    lawsuit_amount: Optional[str] = Field(None, description="Amount of the lawsuit as a string (e.g., '$2,500.00')")
    civil_number: Optional[str] = Field(None, description="Civil case number")
    lawyer_email: Optional[EmailStr] = Field(None, description="Email address of lawyer representing plaintiff")
    law_firm: Optional[Organization] = Field(None, description="Law firm information")
    plaintiff: Optional[Organization] = Field(None, description="Plaintiff organization information")
    customer: Optional[Customer] = Field(None, description="Customer/defendant information")


class LawsuitRequest(BaseModel):
    user_id: str = Field(
        ...,
        description="User ID to restrict the data extraction to this user's documents",
    )
    config: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {},
        description=(
            "Optional configuration for lawsuit data generation. Can include additional parameters "
            "for customizing the extraction process."
        ),
    )
    llm_provider: Optional[str] = Field(
        None,
        description="LLM provider to use (openai, anthropic, google, grok)",
    )
    llm_model: Optional[str] = Field(
        None,
        description="Specific LLM model to use (e.g., gpt-4.1, claude-3-7-sonnet-latest)",
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "user_id": "test_user_2",
                "config": {"additional_context": "Focus on the customer details"},
                "llm_provider": "openai",
                "llm_model": "gpt-4.1",
            }
        }
    }
