from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class DocumentSource(str, Enum):
    FILE = "file"
    TEXT = "text"


class DocumentType(str, Enum):
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    IMAGE = "image"
    JSON = "json"


class DocumentMetadata(BaseModel):
    source: DocumentSource
    user_id: str
    doc_type: Optional[DocumentType] = None
    filename: Optional[str] = None
    page_numbers: Optional[List[int]] = None
    author: Optional[str] = None
    creation_date: Optional[str] = None
    custom_metadata: Optional[dict] = None


class ModelConfig(BaseModel):
    """Configuration for the LLM model used for document processing."""

    llm_provider: Optional[str] = Field(None, description="LLM provider to use (openai, anthropic, google, grok)")
    llm_model: Optional[str] = Field(
        None,
        description="Specific LLM model to use (e.g., gemini-2.5-flash-preview-04-17)",
    )


class DocumentUploadRequest(BaseModel):
    """Request model for document uploads with JSON."""

    user_id: str = Field(..., description="User ID to associate with this document")
    text: Optional[str] = Field(None, description="Text content to process")
    file_content: Optional[str] = Field(None, description="Base64 encoded file content")
    filename: Optional[str] = Field(None, description="Original filename of the uploaded file")
    llm_provider: Optional[str] = Field(None, description="LLM provider to use")
    llm_model: Optional[str] = Field(None, description="Specific LLM model to use")

    model_config = {
        "json_schema_extra": {
            "example": {
                "user_id": "test_user",
                "text": "This is sample text to process",
                "llm_provider": "google",
                "llm_model": "gemini-2.0-flash",
            }
        }
    }


class DocumentRequest(BaseModel):
    text: Optional[str] = None
    metadata: Optional[DocumentMetadata] = None
    llm_config: Optional[ModelConfig] = Field(
        None, description="Configuration for the LLM model used for document processing"
    )


class DocumentResponse(BaseModel):
    document_id: str
    chunks_stored: int
    user_id: str
    status: str = "success"


class DocumentCountRequest(BaseModel):
    """Request model for getting document count."""

    user_id: str = Field(..., description="User ID to get document count for")


class DocumentCountResponse(BaseModel):
    """Response model for document count."""

    count: int = Field(..., description="Number of documents for the user")
    user_id: str = Field(..., description="User ID the count is for")


class DeleteDocumentRequest(BaseModel):
    """Request model for deleting a document."""

    document_id: str = Field(..., description="Document ID to delete")
    user_id: str = Field(..., description="User ID that owns the document")


class DeleteDocumentResponse(BaseModel):
    """Response model for deleting a document."""

    success: bool = Field(..., description="Whether the deletion was successful")
    document_id: str = Field(..., description="ID of the document that was deleted")
    user_id: str = Field(..., description="User ID that owns the document")
    status: str = "success"
