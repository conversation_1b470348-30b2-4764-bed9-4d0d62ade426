from typing import List, Optional

from pydantic import BaseModel, Field


class QueryRequest(BaseModel):
    """Request model for querying documents."""

    user_id: str = Field(..., description="User ID to search documents for")
    query: str = Field(..., description="The question to answer")
    llm_provider: Optional[str] = Field(None, description="LLM provider to use (openai, anthropic, google, grok)")
    llm_model: Optional[str] = Field(
        None,
        description="Specific LLM model to use (e.g., gpt-4.1, claude-3-7-sonnet-latest)",
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "user_id": "test_user_2",
                "query": "What amounts are due and when? Be sure to list totals.",
                "llm_provider": "openai",
                "llm_model": "gpt-4.1",
            }
        }
    }


class QueryResponse(BaseModel):
    """Response model for document queries."""

    answer: str = Field(..., description="Answer to the question")
    sources: List[str] = Field(..., description="List of source document IDs used to generate the answer")
