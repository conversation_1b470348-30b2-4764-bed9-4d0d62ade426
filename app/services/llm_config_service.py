import os
import logging
from functools import lru_cache
from typing import Any, Dict, List, Optional, Tuple, cast

from langchain.adapters import openai as lc_openai_adapter
from langchain.chat_models.base import BaseChatModel
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON>hropic
from langchain_google_genai import <PERSON><PERSON><PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_openai import ChatOpenAI

from app.schemas.llm_config_schema import (
    PROVIDER_MODELS,
    LLMConfig,
    LLMProvider,
    LLMTask,
    ModelConfig,
    ModelName,
    ProviderConfig,
    TaskConfig,
    get_model_key,
)


class LLMConfigService:
    """
    Service to manage LLM configuration settings

    This service provides a centralized way to:
    1. Access configuration for providers, models and tasks
    2. Resolve which provider/model to use for a specific task
    3. Get instances of language models with appropriate settings
    4. Retrieve parameters for operations like chunking and retrieval
    """

    def __init__(self) -> None:
        """Initialize with default configuration"""
        self.config: LLMConfig = self._build_default_config()
        self._model_instances: Dict[str, BaseChatModel] = {}

    def _build_default_config(self) -> LLMConfig:
        """Build the default configuration object"""
        # Define provider configurations
        providers = {
            LLMProvider.OPENAI: ProviderConfig(
                api_key_env="OPENAI_API_KEY",
                default_model=ModelName.GPT_41,
            ),
            LLMProvider.ANTHROPIC: ProviderConfig(
                api_key_env="ANTHROPIC_API_KEY",
                default_model=ModelName.CLAUDE_3_7_SONNET_LATEST,
            ),
            LLMProvider.GOOGLE: ProviderConfig(
                api_key_env="GOOGLE_API_KEY",
                default_model=ModelName.GEMINI_25_PRO_PREVIEW,
            ),
            LLMProvider.GROK: ProviderConfig(
                api_key_env="GROK_API_KEY",
                default_model=ModelName.GROK_3_LATEST,
            ),
        }

        # Define model configurations
        models = {}
        for provider in LLMProvider:
            for model in PROVIDER_MODELS[provider]:
                models[get_model_key(provider, model)] = ModelConfig(
                    provider=provider, temperature=0.0, max_tokens=None
                )

        # Define task configurations
        tasks = {
            LLMTask.QUESTION_ANSWERING: TaskConfig(
                default_provider_model=(LLMProvider.OPENAI, ModelName.GPT_41),
                allowed_providers=list(LLMProvider),
                default_k=4,
                chunk_size=1000,  # Default values
                chunk_overlap=200,  # Default values
            ),
            LLMTask.LAWSUIT_EXTRACTION: TaskConfig(
                default_provider_model=(LLMProvider.OPENAI, ModelName.GPT_41),
                allowed_providers=list(LLMProvider),
                default_k=10,
                chunk_size=1000,  # Default values
                chunk_overlap=200,  # Default values
            ),
            LLMTask.DOCUMENT_EXTRACTION: TaskConfig(
                default_provider_model=(
                    LLMProvider.GOOGLE,
                    ModelName.GEMINI_25_FLASH_PREVIEW,
                ),
                allowed_providers=[LLMProvider.GOOGLE],
                chunk_size=500,  # Reduced for faster processing
                chunk_overlap=100,
                default_k=4,  # Default value
            ),
        }

        # Return the complete configuration
        return LLMConfig(
            providers=providers,
            models=models,
            tasks=tasks,
            global_default=(LLMProvider.OPENAI, ModelName.GPT_41),
        )

    def get_provider_config(self, provider: LLMProvider) -> ProviderConfig:
        """Get provider configuration"""
        return self.config.providers[provider]

    def get_model_config(self, provider: LLMProvider, model_name: ModelName) -> ModelConfig:
        """Get model configuration for a specific provider and model"""
        return self.config.models[get_model_key(provider, model_name)]

    def get_task_config(self, task: LLMTask) -> TaskConfig:
        """Get task configuration for a specific task"""
        return self.config.tasks[task]

    def get_default_provider_model_for_task(self, task: LLMTask) -> Tuple[LLMProvider, ModelName]:
        """Get the default provider and model for a specific task"""
        return self.config.tasks[task].default_provider_model

    def is_provider_allowed_for_task(self, provider: Optional[LLMProvider], task: LLMTask) -> bool:
        """Check if a provider is allowed for a specific task"""
        if provider is None:
            return False
        return provider in self.config.tasks[task].allowed_providers

    def is_model_available_for_provider(self, model_name: ModelName, provider: LLMProvider) -> bool:
        """Check if a model is available for a specific provider"""
        return model_name in PROVIDER_MODELS[provider]

    def get_available_models_for_provider(self, provider: LLMProvider) -> List[ModelName]:
        """Get all available models for a specific provider"""
        return PROVIDER_MODELS[provider]

    def resolve_provider_model(
        self,
        task: LLMTask,
        requested_provider: Optional[str] = None,
        requested_model: Optional[str] = None,
    ) -> Tuple[LLMProvider, ModelName]:
        """
        Resolve which provider and model to use based on the task and requested options

        This method implements the following logic:
        1. Start with the default provider/model for the task
        2. If both provider and model are specified, verify they're compatible
        3. If only a model is requested, use that model with its associated provider
        4. If only a provider is requested, use that provider with its default model
        5. In case of invalid requests, fall back to the defaults

        Args:
            task: The task to be performed
            requested_provider: The provider requested by the user (optional)
            requested_model: The model requested by the user (optional)

        Returns:
            Tuple of (provider, model) to use
        """
        logger = logging.getLogger(__name__)

        # Log the input parameters
        logger.info(
            f"Resolving provider/model for task={task}, "
            f"requested_provider={requested_provider}, "
            f"requested_model={requested_model}"
        )

        # Start with the task default
        provider, model = self.get_default_provider_model_for_task(task)

        # Case 1: Both provider and model are specified
        if requested_provider and requested_model:
            try:
                req_provider = LLMProvider(requested_provider)
                req_model = ModelName(requested_model)

                # Check if the provider is allowed for this task
                if not self.is_provider_allowed_for_task(req_provider, task):
                    # Provider not allowed, stick with default
                    logger.warning(
                        f"Provider {req_provider} not allowed for task {task}, " f"using default: {provider}/{model}"
                    )
                    return provider, model

                # Check if the model is compatible with the requested provider
                model_provider = req_model.provider
                if model_provider != req_provider:
                    # If the model doesn't match the requested provider, use the provider's default model
                    default_model = self.config.providers[req_provider].default_model
                    logger.warning(
                        f"Model {req_model} not compatible with provider {req_provider}, "
                        f"using provider's default model: {default_model}"
                    )
                    return req_provider, default_model

                # Both provider and model are valid and compatible
                logger.info(f"Using requested provider/model: {req_provider}/{req_model}")
                return req_provider, req_model

            except ValueError as e:
                # Invalid provider or model, stick with default
                logger.warning(f"Invalid provider or model: {e}, " f"using default: {provider}/{model}")
                pass

        # Case 2: Only model is specified
        elif requested_model:
            try:
                req_model = ModelName(requested_model)
                req_provider = req_model.provider

                # If provider is None, skip this model
                if req_provider is None:
                    # Model exists but has no provider mapping, stick with default
                    pass
                # Check if the provider is allowed for this task
                elif self.is_provider_allowed_for_task(req_provider, task):
                    # Update both provider and model
                    return req_provider, req_model
            except ValueError:
                # Invalid model, stick with default
                pass

        # Case 3: Only provider is specified
        elif requested_provider:
            try:
                req_provider = LLMProvider(requested_provider)
                if self.is_provider_allowed_for_task(req_provider, task):
                    # Use the requested provider with its default model
                    return (
                        req_provider,
                        self.config.providers[req_provider].default_model,
                    )
            except ValueError:
                # Invalid provider, stick with default
                pass

        # Return the defaults if nothing valid was requested
        return provider, model

    def get_model_instance(self, provider: LLMProvider, model_name: ModelName) -> BaseChatModel:
        """
        Get or create a model instance for the specified provider and model

        This method:
        1. Checks the cache for an existing instance
        2. Validates that the model belongs to the specified provider
        3. Creates a provider-specific model instance with the right configuration
        4. Caches the instance for future use

        Args:
            provider: The LLM provider
            model_name: The model name

        Returns:
            An instance of a LangChain chat model
        """
        # Create a unique key for this provider/model combination
        model_key = get_model_key(provider, model_name)

        # Return cached instance if available
        if model_key in self._model_instances:
            return self._model_instances[model_key]

        # Get model config
        model_config = self.get_model_config(provider, model_name)

        # Validate provider and model match
        model_provider = model_name.provider
        if model_provider is None or model_provider != provider:
            raise ValueError(f"Model {model_name} does not belong to provider {provider}")

        # Common parameters for all providers
        common_params = {
            "temperature": model_config.temperature,
            **model_config.additional_params,
        }

        # Create new instance based on provider
        if provider == LLMProvider.OPENAI:
            # Use kwargs to handle optional parameters
            kwargs = {**common_params}
            if model_config.max_tokens is not None:
                kwargs["max_tokens"] = model_config.max_tokens

            result_model: BaseChatModel = ChatOpenAI(model=model_name.value, **kwargs)
        elif provider == LLMProvider.ANTHROPIC:
            # Use kwargs to handle optional parameters
            kwargs = {**common_params}
            if model_config.max_tokens is not None:
                kwargs["max_tokens"] = model_config.max_tokens

            result_model = ChatAnthropic(model_name=model_name.value, **kwargs)
        elif provider == LLMProvider.GOOGLE:
            # Use kwargs to handle optional parameters
            kwargs = {**common_params}
            if model_config.max_tokens is not None:
                kwargs["max_output_tokens"] = model_config.max_tokens

            result_model = ChatGoogleGenerativeAI(model=model_name.value, **kwargs)
        elif provider == LLMProvider.GROK:
            # Use OpenAI adapter for Grok models
            grok_params = {
                "api_key": os.getenv("GROK_API_KEY"),
                "base_url": os.getenv("GROK_API_BASE", "https://api.grok.x/v1"),
            }

            # Create a custom adapter for Grok using the OpenAI adapter
            result_model = cast(
                BaseChatModel,
                lc_openai_adapter.to_chat_model(
                    provider="grok",
                    model=model_name.value,
                    temperature=model_config.temperature,
                    max_tokens=model_config.max_tokens,
                    api_key=grok_params["api_key"],
                    base_url=grok_params["base_url"],
                    **common_params,
                ),
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Cache and return
        self._model_instances[model_key] = result_model
        return result_model

    def get_retrieval_params(self, task: LLMTask) -> Dict[str, Any]:
        """Get retrieval parameters for a specific task"""
        task_config = self.get_task_config(task)
        return {
            "k": task_config.default_k,
        }

    def get_chunking_params(self, task: LLMTask) -> Dict[str, int]:
        """Get chunking parameters for a specific task"""
        task_config = self.get_task_config(task)
        return {
            "chunk_size": task_config.chunk_size,
            "chunk_overlap": task_config.chunk_overlap,
        }

    def get_inference_params(self, task: LLMTask) -> Dict[str, Any]:
        """Get inference parameters for a specific task"""
        task_config = self.get_task_config(task)
        return {
            "chunk_size": task_config.chunk_size,
            "chunk_overlap": task_config.chunk_overlap,
            "k": task_config.default_k,
        }


@lru_cache()
def get_llm_config_service() -> LLMConfigService:
    """
    Factory function for LLM config service with caching

    Uses lru_cache to ensure there's only one instance across the application
    """
    return LLMConfigService()
