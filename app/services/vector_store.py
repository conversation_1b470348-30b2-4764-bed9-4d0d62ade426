import os
import uuid
from typing import Any, Dict, List, Optional, TypedDict, cast

from dotenv import load_dotenv
from langchain.embeddings.base import Embeddings
from langchain.schema import Document
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings

load_dotenv()

# Define our own Where type since it might not be available in all langchain versions
Where = Dict[str, Any]


# Define a type alias for Chroma Where filter
class WhereFilter(TypedDict, total=False):
    and_clause: List[Dict[str, Any]]
    or_clause: List[Dict[str, Any]]


class VectorStoreService:
    """Service to handle document embeddings and vector store operations."""

    def __init__(self, embedding_model: Optional[Embeddings] = None):
        self.persist_directory = os.getenv("CHROMA_PERSIST_DIRECTORY", "./data/chroma")
        os.makedirs(self.persist_directory, exist_ok=True)

        self.embedding_model = embedding_model or OpenAIEmbeddings()
        self.vector_store = Chroma(
            persist_directory=self.persist_directory,
            embedding_function=self.embedding_model,
        )

    def add_documents(
        self,
        documents: List[Document],
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Add documents to the vector store and return document ID."""
        if not documents:
            raise ValueError("No documents provided for embedding")

        # Generate a unique ID for this document set
        document_id = str(uuid.uuid4())

        # Prepare metadata for each document
        for doc in documents:
            doc.metadata = {
                **(doc.metadata or {}),
                "document_id": document_id,
                "user_id": user_id,
                **(metadata or {}),
            }

        # Add to vector store and persist changes
        self.vector_store.add_documents(documents)
        self.vector_store.persist()

        return document_id

    def search_documents(self, query: str, user_id: str, k: int = 4) -> List[Document]:
        """Search for relevant documents based on the query and filtered by user_id."""
        return self.vector_store.similarity_search(query, k=k, filter={"user_id": user_id})

    def delete_document(self, document_id: str, user_id: str) -> None:
        """Delete a document by its ID, only if it belongs to the specified user."""
        # Use IDs instead of a complex filter
        where_filter = cast(Where, {"$and": [{"document_id": document_id}, {"user_id": user_id}]})
        docs = self.vector_store.get(where=where_filter)
        if docs and docs.get("ids"):
            self.vector_store.delete(ids=docs["ids"])
            self.vector_store.persist()

    def get_document_count(self, user_id: Optional[str] = None) -> int:
        """
        Get the total number of documents in the vector store.

        If user_id is provided, returns the count for that specific user.
        """
        where_filter = None
        if user_id:
            where_filter = cast(Where, {"user_id": user_id})

        result = self.vector_store.get(where=where_filter)
        return len(result["ids"]) if "ids" in result else 0

    def clear(self, user_id: Optional[str] = None) -> None:
        """
        Clear documents from the vector store.

        If user_id is provided, only clears documents for that specific user.
        """
        if user_id:
            where_filter = cast(Where, {"user_id": user_id})
            docs = self.vector_store.get(where=where_filter)
            if docs and docs.get("ids"):
                self.vector_store.delete(ids=docs["ids"])
        else:
            self.vector_store._collection.delete()
        self.vector_store.persist()
