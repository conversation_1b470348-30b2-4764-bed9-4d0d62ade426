import json
import logging
import os
import time
import uuid
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, UTC
import psycopg2
from uuid import UUID

from langchain_core.callbacks.base import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseCallbackHandler
from langchain_core.messages import BaseMessage, get_buffer_string
from langchain_core.outputs import LLMResult

from app.schemas.llm_trace_schema import LLMTraceCreate, TraceStatus
from app.services.llm_trace_service import LLMTraceService

logger = logging.getLogger(__name__)


class PostgresTraceCallbackHandler(AsyncCallbackHandler, BaseCallbackHandler):
    """
    Callback handler for LangChain that logs all LLM interactions to PostgreSQL.

    This handler captures prompts, responses, and metadata about each LLM call
    and stores them for later analysis and evaluation.
    """

    def __init__(
        self,
        trace_service: Optional[LLMTraceService] = None,
        postgres_url: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        trace_filter: Optional[Callable[[Dict[str, Any]], bool]] = None,
    ):
        """
        Initialize the PostgreSQL trace callback handler.

        Args:
            trace_service: Optional LLMTraceService instance to use
            postgres_url: PostgreSQL connection URL (if trace_service not provided)
            user_id: Optional user ID to associate with all traces
            session_id: Optional session ID to group related traces
            tags: Optional list of tags to apply to all traces
            trace_filter: Optional function to filter which LLM calls to trace
        """
        self.trace_service = None
        self.postgres_url = postgres_url or os.getenv("POSTGRES_URL")
        self.user_id = user_id
        self.session_id = session_id or str(uuid.uuid4())
        self.tags = tags or []
        self.trace_filter = trace_filter

        # Store active traces by run ID
        self.active_traces: Dict[str, Dict[str, Any]] = {}

        # Flag to indicate if the service is initialized
        self._is_initialized = False

        # Load immediately if trace_service is provided
        if trace_service:
            self.trace_service = trace_service
            self._is_initialized = True

    async def _ensure_initialized(self) -> None:
        """Ensure the trace service is initialized."""
        if not self._is_initialized:
            if not self.postgres_url:
                logger.warning("PostgreSQL URL not provided, tracing disabled")
                self._is_initialized = True
                return

            try:
                self.trace_service = LLMTraceService(postgres_url=self.postgres_url)
                await self.trace_service.initialize()
                self._is_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize LLM trace service: {str(e)}")
                self._is_initialized = True  # Prevent further initialization attempts

    def _should_trace(self, metadata: Dict[str, Any]) -> bool:
        """Check if we should trace this call based on metadata."""
        return bool(self.trace_service and metadata.get("trace", True))

    def _get_trace_data(self, run_id: UUID) -> Optional[Dict[str, Any]]:
        """Get trace data for a run ID."""
        return self.active_traces.pop(str(run_id), None)

    async def on_llm_start(
        self,
        serialized: Dict[str, Any],
        prompts: List[str],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Log the start of an LLM call."""
        await self._ensure_initialized()

        if not self.trace_service or not self._should_trace(metadata or {}):
            return

        # Extract actual provider and model info
        provider = "unknown"
        model_name = "unknown"

        # Try to extract from serialized data
        if serialized:
            # Extract provider and model from id field first
            if "id" in serialized and serialized["id"]:
                id_parts = (
                    serialized["id"][0].split("/")
                    if isinstance(serialized["id"], list)
                    else serialized["id"].split("/")
                )
                if len(id_parts) > 0:
                    provider = id_parts[0]
                if len(id_parts) > 1:
                    model_name = id_parts[-1]

            # Override with kwargs if present
            if "kwargs" in serialized:
                if "model" in serialized["kwargs"]:
                    model_name = serialized["kwargs"]["model"]
                elif "model_name" in serialized["kwargs"]:
                    model_name = serialized["kwargs"]["model_name"]

            # Override with metadata if available
            if metadata:
                if "provider" in metadata:
                    provider = metadata["provider"]
                if "model" in metadata:
                    model_name = metadata["model"]

            # Clean up model name if it includes provider
            if "/" in model_name:
                model_parts = model_name.split("/")
                if len(model_parts) > 1:
                    model_name = model_parts[-1]

        # Log extraction
        logger.debug(f"Extracted provider={provider}, model={model_name} from LLM call")

        # Record start time
        start_time = time.time()

        combined_tags = (self.tags or []) + (tags or [])

        # Ensure run_id is a string (in case it's a UUID object)
        request_id = str(run_id) if run_id else None

        # Create trace entry
        self.active_traces[str(run_id)] = {
            "start_time": start_time,
            "provider": provider,
            "model": model_name,
            "prompt": prompts[0] if prompts else "",
            "parameters": serialized.get("kwargs", {}),
            "metadata": metadata or {},
            "tags": combined_tags,
            "parent_run_id": str(parent_run_id) if parent_run_id else None,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": request_id,
        }

    async def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Log the start of a chat model call."""
        await self._ensure_initialized()

        if not self.trace_service or not self._should_trace(metadata or {}):
            return

        # Extract actual provider and model info
        provider = "unknown"
        model_name = "unknown"

        # Try to extract from serialized data
        if serialized:
            # Extract from invocation params
            if "kwargs" in serialized and "model" in serialized["kwargs"]:
                model_name = serialized["kwargs"]["model"]
            elif "kwargs" in serialized and "model_name" in serialized["kwargs"]:
                model_name = serialized["kwargs"]["model_name"]

            # Extract provider
            if "id" in serialized and serialized["id"]:
                id_parts = (
                    serialized["id"][0].split("/")
                    if isinstance(serialized["id"], list)
                    else serialized["id"].split("/")
                )
                if len(id_parts) > 0:
                    provider = id_parts[0]

            # Extract from metadata if available
            if metadata:
                if "provider" in metadata:
                    provider = metadata["provider"]
                if "model" in metadata:
                    model_name = metadata["model"]

            # Clean up model name if it includes provider
            if "/" in model_name:
                model_parts = model_name.split("/")
                if len(model_parts) > 1:
                    model_name = model_parts[-1]

        # Log extraction
        logger.debug(f"Extracted provider={provider}, model={model_name} from Chat LLM call")

        # Record start time
        start_time = time.time()

        combined_tags = (self.tags or []) + (tags or [])

        # Convert messages to a serializable format
        prompt: List[Dict[str, Any]] = []
        try:
            if messages and messages[0]:
                for message in messages[0]:
                    prompt.append(
                        {
                            "role": message.type,
                            "content": message.content,
                        }
                    )

            if not prompt:
                # Fallback to string representation
                prompt = [
                    {
                        "role": "system",
                        "content": get_buffer_string(messages[0]) if messages and messages[0] else "",
                    }
                ]
        except Exception as e:
            logger.warning(f"Error serializing chat messages: {str(e)}")
            prompt = [{"role": "system", "content": str(messages)}]

        # Ensure run_id is a string (in case it's a UUID object)
        request_id = str(run_id) if run_id else None

        # Create trace entry
        self.active_traces[str(run_id)] = {
            "start_time": start_time,
            "provider": provider,
            "model": model_name,
            "prompt": prompt,
            "parameters": serialized.get("kwargs", {}),
            "metadata": metadata or {},
            "tags": combined_tags,
            "parent_run_id": str(parent_run_id) if parent_run_id else None,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": request_id,
        }

    async def on_llm_end(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> None:
        """Log the response from an LLM call."""
        await self._ensure_initialized()

        if not self.trace_service or str(run_id) not in self.active_traces:
            return

        # Calculate duration and get trace data
        trace_data = self.active_traces.pop(str(run_id), {})
        start_time = trace_data.get("start_time", time.time())
        duration_ms = int((time.time() - start_time) * 1000)

        # Get response text
        response_text = ""
        if response.generations and response.generations[0]:
            response_text = response.generations[0][0].text

        # Count tokens if available
        usage = response.llm_output.get("token_usage", {}) if response.llm_output else {}
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)
        total_tokens = usage.get("total_tokens", 0) or (prompt_tokens + completion_tokens)

        # Create trace object
        try:
            trace = LLMTraceCreate(
                provider=trace_data.get("provider", "unknown"),
                model=trace_data.get("model", "unknown"),
                prompt=trace_data.get("prompt", ""),
                prompt_tokens=prompt_tokens,
                response=response_text,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                status=TraceStatus.SUCCESS,
                error_message=None,
                duration_ms=duration_ms,
                parameters=trace_data.get("parameters", {}),
                metadata=trace_data.get("metadata", {}),
                tags=trace_data.get("tags", []),
                user_id=trace_data.get("user_id"),
                session_id=trace_data.get("session_id"),
                request_id=trace_data.get("request_id"),
            )

            # Use the sync method to avoid event loop issues
            self._create_trace_sync(trace)
            logger.info(f"Successfully traced LLM call: {trace_data.get('provider')}/{trace_data.get('model')}")

        except Exception as e:
            logger.error(f"Error creating trace: {str(e)}", exc_info=True)

    async def on_llm_error(
        self,
        error: BaseException,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> None:
        """Log an error during an LLM call."""
        await self._ensure_initialized()

        if not self.trace_service or str(run_id) not in self.active_traces:
            return

        # Calculate duration
        trace_data = self.active_traces.pop(str(run_id), {})
        start_time = trace_data.get("start_time", time.time())
        duration_ms = int((time.time() - start_time) * 1000)

        # Ensure request_id is a string
        request_id = str(trace_data.get("request_id")) if trace_data.get("request_id") else None

        # Log error details for debugging
        logger.error(f"LLM error during {trace_data.get('provider')}/{trace_data.get('model')} call: {str(error)}")

        # Create trace object
        try:
            trace = LLMTraceCreate(
                provider=trace_data.get("provider", "unknown"),
                model=trace_data.get("model", "unknown"),
                prompt=trace_data.get("prompt", ""),
                prompt_tokens=0,  # Will be updated by token counting
                response=str(error),
                completion_tokens=0,  # No completion on error
                total_tokens=0,  # Will be updated by token counting
                status=TraceStatus.ERROR,
                error_message=str(error),
                duration_ms=duration_ms,
                parameters=trace_data.get("parameters", {}),
                metadata=trace_data.get("metadata", {}),
                tags=trace_data.get("tags", []),
                user_id=trace_data.get("user_id"),
                session_id=trace_data.get("session_id"),
                request_id=request_id,
            )

            # Directly use the sync method which is designed to avoid event loop issues
            self._create_trace_sync(trace)
            logger.info(f"Successfully traced LLM error: {trace_data.get('provider')}/{trace_data.get('model')}")

        except Exception as e:
            logger.error(f"Failed to save LLM error trace: {str(e)}")

    def _create_trace_sync(self, trace: LLMTraceCreate) -> None:
        """Create a trace record synchronously."""
        try:
            # Get DB URL from self or trace_service
            db_url = self.postgres_url or (self.trace_service and getattr(self.trace_service, "postgres_url", None))
            if not db_url:
                logger.error("No database URL available for synchronous trace creation")
                return
            if db_url.startswith("postgresql+asyncpg://"):
                db_url = db_url.replace("postgresql+asyncpg://", "postgresql://", 1)
            conn = psycopg2.connect(db_url)
            try:
                with conn.cursor() as cur:
                    # Generate a new UUID for id
                    trace_id = getattr(trace, "id", None) or str(uuid.uuid4())
                    # Insert trace record with exact schema order
                    cur.execute(
                        """
                        INSERT INTO llm_traces (
                            id, user_id, session_id, request_id, provider, model,
                            prompt, prompt_tokens, response, completion_tokens,
                            total_tokens, status, error_message, duration_ms,
                            parameters, metadata, tags, created_at
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """,
                        (
                            trace_id,
                            getattr(trace, "user_id", None),
                            getattr(trace, "session_id", None),
                            getattr(trace, "request_id", None),
                            getattr(trace, "provider", None),
                            getattr(trace, "model", None),
                            (
                                json.dumps(getattr(trace, "prompt", None))
                                if getattr(trace, "prompt", None) is not None
                                else None
                            ),
                            getattr(trace, "prompt_tokens", None),
                            (
                                json.dumps(getattr(trace, "response", None))
                                if getattr(trace, "response", None) is not None
                                else None
                            ),
                            getattr(trace, "completion_tokens", None),
                            getattr(trace, "total_tokens", None),
                            getattr(trace, "status", None),
                            getattr(trace, "error_message", None),
                            getattr(trace, "duration_ms", None),
                            (
                                json.dumps(getattr(trace, "parameters", None))
                                if getattr(trace, "parameters", None) is not None
                                else None
                            ),
                            (
                                json.dumps(getattr(trace, "metadata", None))
                                if getattr(trace, "metadata", None) is not None
                                else None
                            ),
                            getattr(trace, "tags", None),
                            datetime.now(UTC),
                        ),
                    )
                conn.commit()
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"Error creating trace record: {str(e)}", exc_info=True)
            raise

    def on_llm_start_sync(
        self,
        serialized: Dict[str, Any],
        prompts: List[str],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Sync version of on_llm_start."""
        # Simplified sync implementation - just store trace data
        if not self.trace_service and not self.postgres_url:
            return

        # Extract provider and model info (simplified from async version)
        provider = "unknown"
        model_name = "unknown"

        if serialized:
            if "id" in serialized and serialized["id"]:
                id_parts = (
                    serialized["id"][0].split("/")
                    if isinstance(serialized["id"], list)
                    else serialized["id"].split("/")
                )
                if len(id_parts) > 0:
                    provider = id_parts[0]
                if len(id_parts) > 1:
                    model_name = id_parts[-1]

            if "kwargs" in serialized:
                if "model" in serialized["kwargs"]:
                    model_name = serialized["kwargs"]["model"]

        # Record start time and store trace data
        start_time = time.time()
        combined_tags = (self.tags or []) + (tags or [])

        self.active_traces[str(run_id)] = {
            "start_time": start_time,
            "provider": provider,
            "model": model_name,
            "prompt": prompts[0] if prompts else "",
            "parameters": serialized.get("kwargs", {}),
            "metadata": metadata or {},
            "tags": combined_tags,
            "parent_run_id": str(parent_run_id) if parent_run_id else None,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": str(run_id),
        }

    def on_chat_model_start_sync(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Sync version of on_chat_model_start."""
        # Simplified sync implementation - just store trace data
        if not self.trace_service and not self.postgres_url:
            return

        # Extract provider and model info (simplified)
        provider = "unknown"
        model_name = "unknown"

        if serialized:
            if "kwargs" in serialized and "model" in serialized["kwargs"]:
                model_name = serialized["kwargs"]["model"]
            if "id" in serialized and serialized["id"]:
                id_parts = (
                    serialized["id"][0].split("/")
                    if isinstance(serialized["id"], list)
                    else serialized["id"].split("/")
                )
                if len(id_parts) > 0:
                    provider = id_parts[0]

        # Convert messages to serializable format
        prompt = []
        try:
            if messages and messages[0]:
                for message in messages[0]:
                    prompt.append({"role": message.type, "content": message.content})
        except Exception:
            prompt = [{"role": "system", "content": str(messages)}]

        # Record start time and store trace data
        start_time = time.time()
        combined_tags = (self.tags or []) + (tags or [])

        self.active_traces[str(run_id)] = {
            "start_time": start_time,
            "provider": provider,
            "model": model_name,
            "prompt": prompt,
            "parameters": serialized.get("kwargs", {}),
            "metadata": metadata or {},
            "tags": combined_tags,
            "parent_run_id": str(parent_run_id) if parent_run_id else None,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": str(run_id),
        }

    def on_llm_end_sync(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> None:
        """Sync version of on_llm_end."""
        if str(run_id) not in self.active_traces:
            return

        # Get trace data and calculate duration
        trace_data = self.active_traces.pop(str(run_id), {})
        start_time = trace_data.get("start_time", time.time())
        duration_ms = int((time.time() - start_time) * 1000)

        # Extract response and token usage
        response_text = ""
        if response.generations and response.generations[0]:
            response_text = response.generations[0][0].text

        usage = response.llm_output.get("token_usage", {}) if response.llm_output else {}
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)
        total_tokens = usage.get("total_tokens", 0) or (prompt_tokens + completion_tokens)

        # Create and save trace synchronously
        try:
            trace = LLMTraceCreate(
                provider=trace_data.get("provider", "unknown"),
                model=trace_data.get("model", "unknown"),
                prompt=trace_data.get("prompt", ""),
                prompt_tokens=prompt_tokens,
                response=response_text,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                status=TraceStatus.SUCCESS,
                error_message=None,
                duration_ms=duration_ms,
                parameters=trace_data.get("parameters", {}),
                metadata=trace_data.get("metadata", {}),
                tags=trace_data.get("tags", []),
                user_id=trace_data.get("user_id"),
                session_id=trace_data.get("session_id"),
                request_id=trace_data.get("request_id"),
            )
            self._create_trace_sync(trace)
        except Exception as e:
            logger.error(f"Error in sync LLM end handler: {str(e)}")

    def on_llm_error_sync(
        self,
        error: BaseException,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> None:
        """Sync version of on_llm_error."""
        if str(run_id) not in self.active_traces:
            return

        # Get trace data and calculate duration
        trace_data = self.active_traces.pop(str(run_id), {})
        start_time = trace_data.get("start_time", time.time())
        duration_ms = int((time.time() - start_time) * 1000)

        # Create and save error trace synchronously
        try:
            trace = LLMTraceCreate(
                provider=trace_data.get("provider", "unknown"),
                model=trace_data.get("model", "unknown"),
                prompt=trace_data.get("prompt", ""),
                prompt_tokens=0,
                response=str(error),
                completion_tokens=0,
                total_tokens=0,
                status=TraceStatus.ERROR,
                error_message=str(error),
                duration_ms=duration_ms,
                parameters=trace_data.get("parameters", {}),
                metadata=trace_data.get("metadata", {}),
                tags=trace_data.get("tags", []),
                user_id=trace_data.get("user_id"),
                session_id=trace_data.get("session_id"),
                request_id=str(trace_data.get("request_id")),
            )
            self._create_trace_sync(trace)
        except Exception as e:
            logger.error(f"Error in sync LLM error handler: {str(e)}")
