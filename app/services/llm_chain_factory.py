import os
import logging
from typing import Any, List, Optional, Union, cast

from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.runnables import RunnableSequence
from langchain.chains.base import Chain
from langchain.chains import LL<PERSON>hain, ConversationChain
from langchain_google_genai import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain.adapters import openai as lc_openai_adapter

from app.services.llm_trace_callback import PostgresTraceCallbackHandler
from app.services.llm_trace_service import LLMTraceService
from app.schemas.llm_config_schema import LLMProvider, ModelName, LLMTask

logger = logging.getLogger(__name__)


class LLMChainFactory:
    """Factory for creating LangChain chains with PostgreSQL tracing."""

    def __init__(
        self,
        postgres_url: Optional[str] = None,
        trace_service: Optional[LLMTraceService] = None,
        default_model: str = ModelName.GEMINI_20_FLASH.value,
        default_provider: str = LLMProvider.GOOGLE.value,
    ):
        """
        Initialize the LLM chain factory.

        Args:
            postgres_url: URL for PostgreSQL connection
            trace_service: Optional existing trace service
            default_model: Default model name to use
            default_provider: Default provider to use
        """
        self.postgres_url = postgres_url or os.getenv("POSTGRES_URL")
        self.trace_service = trace_service
        self.default_model = default_model
        self.default_provider = default_provider

        # Cache trace service to reuse
        self._trace_service_initialized = False
        if trace_service:
            self._trace_service_initialized = True

    async def initialize(self) -> None:
        """Initialize tracing service if not already done."""
        if not self._trace_service_initialized and self.postgres_url:
            try:
                self.trace_service = LLMTraceService(postgres_url=self.postgres_url)
                await self.trace_service.initialize()
                self._trace_service_initialized = True
                logger.info("Initialized LLM trace service for chain factory")
            except Exception as e:
                logger.error(f"Failed to initialize LLM trace service: {str(e)}")

    def _create_callback_handler(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
    ) -> PostgresTraceCallbackHandler:
        """Create a callback handler for LLM tracing."""
        # Create a new trace callback handler
        handler = PostgresTraceCallbackHandler(
            trace_service=self.trace_service,
            postgres_url=self.postgres_url,
            user_id=user_id,
            session_id=session_id,
            tags=tags,
        )
        return handler

    def create_llm(
        self,
        model_name: Optional[str] = None,
        provider: Optional[str] = None,
        temperature: float = 0.0,
        top_p: Optional[float] = None,
        top_k: Optional[int] = None,
        max_tokens: Optional[int] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> BaseChatModel:
        """
        Create an LLM with appropriate callbacks for tracing.

        Args:
            model_name: The model to use
            provider: The provider (Google, OpenAI, etc.)
            temperature: Controls randomness (0.0 = deterministic, 1.0 = creative)
            top_p: Nucleus sampling parameter
            top_k: Limits vocabulary options
            max_tokens: Maximum output tokens
            user_id: User ID for tracing
            session_id: Session ID for tracing
            tags: Tags for categorizing the traces
            **kwargs: Additional arguments to pass to the LLM
        """
        # Use defaults if not provided
        model = model_name or self.default_model
        provider_name = provider or self.default_provider

        # Create the callback handler
        handler = self._create_callback_handler(
            user_id=user_id,
            session_id=session_id,
            tags=tags or [],
        )

        # Add task tag if provided in kwargs
        task = kwargs.pop("task", None)
        if task:
            if isinstance(task, LLMTask):
                handler.tags.append(f"task:{task.value}")
            else:
                handler.tags.append(f"task:{task}")

        # Add provider and model info to metadata for accurate tracing
        metadata = kwargs.pop("metadata", {})
        metadata.update(
            {
                "provider": provider_name,
                "model": model,
            }
        )
        kwargs["metadata"] = metadata

        # Create the appropriate LLM based on provider
        if provider_name == LLMProvider.GOOGLE.value:
            return ChatGoogleGenerativeAI(
                model=model,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                max_tokens=max_tokens,
                callbacks=[handler],
                **kwargs,
            )
        elif provider_name == LLMProvider.OPENAI.value:
            return ChatOpenAI(
                model=model,
                temperature=temperature,
                callbacks=[handler],
                **kwargs,
            )
        elif provider_name == LLMProvider.ANTHROPIC.value:
            return ChatAnthropic(
                model_name=model,
                temperature=temperature,
                max_tokens_to_sample=max_tokens,
                callbacks=[handler],
                **kwargs,
            )
        elif provider_name == LLMProvider.GROK.value:
            # Use OpenAI adapter for Grok models
            grok_params = {
                "api_key": os.getenv("GROK_API_KEY"),
                "base_url": os.getenv("GROK_API_BASE", "https://api.grok.x/v1"),
            }

            return cast(
                BaseChatModel,
                lc_openai_adapter.to_chat_model(
                    provider="grok",
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    api_key=grok_params["api_key"],
                    base_url=grok_params["base_url"],
                    callbacks=[handler],
                    **kwargs,
                ),
            )
        else:
            # If provider is not recognized, log an error and use OpenAI
            logger.error(f"Provider {provider_name} not supported, falling back to OpenAI")
            return ChatOpenAI(
                model=model,
                temperature=temperature,
                callbacks=[handler],
                **kwargs,
            )

    def create_chain(
        self,
        prompt: Union[str, PromptTemplate, ChatPromptTemplate],
        output_parser: Optional[BaseOutputParser] = None,
        model_name: Optional[str] = None,
        provider: Optional[str] = None,
        temperature: float = 0.0,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> Union[Chain, RunnableSequence[Any, Any]]:
        """
        Create a chain with the given prompt and output parser.

        Args:
            prompt: The prompt template or string to use
            output_parser: Optional output parser for structured extraction
            model_name: Model name to use
            provider: Provider to use
            temperature: Controls randomness
            user_id: User ID for tracing
            session_id: Session ID for tracing
            tags: Tags for categorizing traces
            **kwargs: Additional arguments for the LLM
        """
        # Create the LLM with tracing
        llm = self.create_llm(
            model_name=model_name,
            provider=provider,
            temperature=temperature,
            user_id=user_id,
            session_id=session_id,
            tags=tags,
            **kwargs,
        )

        # Convert string prompt to a PromptTemplate if needed
        if isinstance(prompt, str):
            prompt = PromptTemplate.from_template(prompt)

        # Create chain with or without output parser
        if output_parser:
            # Create a LCEL chain
            chain = prompt | llm | output_parser
            return cast(RunnableSequence[Any, Any], chain)
        else:
            # Create a standard LLMChain
            return LLMChain(
                llm=llm,
                prompt=prompt,
            )

    def create_conversation_chain(
        self,
        model_name: Optional[str] = None,
        provider: Optional[str] = None,
        temperature: float = 0.0,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> ConversationChain:
        """
        Create a conversation chain for chat-like interactions.

        Args:
            model_name: Model name to use
            provider: Provider to use
            temperature: Controls randomness
            user_id: User ID for tracing
            session_id: Session ID for tracing
            tags: Tags for categorizing traces
            **kwargs: Additional arguments for the LLM
        """
        # Create the LLM with tracing
        llm = self.create_llm(
            model_name=model_name,
            provider=provider,
            temperature=temperature,
            user_id=user_id,
            session_id=session_id,
            tags=tags,
            **kwargs,
        )

        # Create ConversationChain
        return ConversationChain(llm=llm)
