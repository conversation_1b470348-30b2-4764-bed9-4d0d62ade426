# Organize imports properly
# Standard library imports
import os
import uuid
import base64
from typing import Any, Dict, List, Optional, Tuple
import logging

# Third-party imports
from dotenv import load_dotenv
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    Docx2txtLoader,
    TextLoader,
    UnstructuredFileLoader,
    PyPDFLoader,
)
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.callbacks import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseCallbackManager
from langchain_google_genai import ChatGoogleGenerativeAI
import google.generativeai as genai

# >>> ADDED IMPORT
from io import BytesIO

try:
    from pdf2image import convert_from_bytes
except ImportError:
    convert_from_bytes = None
# <<< ADDED IMPORT

# Local imports
from app.schemas.document_schema import DocumentType
from app.schemas.llm_config_schema import <PERSON><PERSON><PERSON>, LL<PERSON>rovider, ModelName
from app.services.llm_config_service import LLMConfigService
from app.services.llm_chain_factory import LLMChainFactory

load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))

logger = logging.getLogger(__name__)

# Mapping of file extensions to document types
EXTENSION_TO_DOC_TYPE = {
    "pdf": DocumentType.PDF,
    "doc": DocumentType.DOCX,
    "docx": DocumentType.DOCX,
    "txt": DocumentType.TXT,
    "jpg": DocumentType.IMAGE,
    "jpeg": DocumentType.IMAGE,
    "png": DocumentType.IMAGE,
    "gif": DocumentType.IMAGE,
    "webp": DocumentType.IMAGE,
    "json": DocumentType.JSON,
}

# Mapping of document types to MIME types
DOC_TYPE_TO_MIME = {
    DocumentType.PDF: "application/pdf",
    DocumentType.IMAGE: "image/jpeg",  # Default for images
    DocumentType.DOCX: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
}

# Default model for faster document processing
DEFAULT_PROVIDER = LLMProvider.GOOGLE
DEFAULT_MODEL = ModelName.GEMINI_20_FLASH

# Add option for more accurate processing when needed
ACCURATE_MODEL = ModelName.GEMINI_25_PRO_PREVIEW


class LLMTextExtractor:
    """Abstract base class for LLM-based text extraction."""

    model_name: str = "base-model"

    def extract_text(self, file_content: bytes, file_name: str, doc_type: DocumentType) -> str:
        """Extract text from a file using LLM."""
        raise NotImplementedError


class GeminiTextExtractor(LLMTextExtractor):
    """Text extraction using Google's Gemini model via LangChain."""

    def __init__(
        self,
        model_name: str,
        callbacks: Optional[List[BaseCallbackHandler]] = None,
        llm: Optional[ChatGoogleGenerativeAI] = None,
    ):
        self.model_name = model_name
        self.callbacks = callbacks

        # Use provided LLM or create a new one
        if llm and isinstance(llm, ChatGoogleGenerativeAI):
            self.llm = llm
        else:
            # Initialize LangChain's ChatGoogleGenerativeAI
            self.llm = ChatGoogleGenerativeAI(
                model=model_name,
                temperature=0.0,
                convert_system_message_to_human=True,
                callbacks=callbacks,
            )

        logger.info(f"Initialized Gemini text extractor with model: {model_name}")

        # Create prompt templates using LangChain format
        self.prompt_templates = {
            DocumentType.PDF: ChatPromptTemplate.from_messages(
                [
                    SystemMessage(
                        content="""
                    Please extract all text content from this PDF document.
                    Return the text in a clean, readable format, preserving the document's structure.
                    Include headers, footers, and any other text elements.
                """
                    ),
                    HumanMessage(content="[PDF CONTENT WILL BE PROVIDED HERE]"),
                ]
            ),
            DocumentType.IMAGE: ChatPromptTemplate.from_messages(
                [
                    SystemMessage(
                        content="""
                    Please extract all text content from this image.
                    Return the text in a clean, readable format, preserving the document's structure.
                    Include any visible text, labels, or annotations.
                """
                    ),
                    HumanMessage(content="[IMAGE CONTENT WILL BE PROVIDED HERE]"),
                ]
            ),
            DocumentType.DOCX: ChatPromptTemplate.from_messages(
                [
                    SystemMessage(
                        content="""
                    Please extract all text content from this Word document.
                    Return the text in a clean, readable format, preserving the document's structure.
                    Include headers, footers, and any other text elements.
                """
                    ),
                    HumanMessage(content="[DOCX CONTENT WILL BE PROVIDED HERE]"),
                ]
            ),
        }

    def extract_text(self, file_content: bytes, file_name: str, doc_type: DocumentType) -> str:
        """Extract text from a file using Gemini with LangChain."""
        logger.info(f"Extracting text with Gemini model {self.model_name}: {file_name}")

        extracted_texts = []  # Store text from each page
        
        if doc_type == DocumentType.PDF:
            if convert_from_bytes:
                try:
                    logger.info("PDF detected. Converting to images for Gemini OCR.")
                    # Convert ALL pages to images
                    images = convert_from_bytes(file_content, fmt="png")
                    
                    if images:
                        # Process up to 50 pages to handle larger legal documents
                        max_pages = min(len(images), 50)
                        logger.info(f"PDF has {len(images)} pages. Processing first {max_pages} pages.")
                        
                        # Process each page
                        for i in range(max_pages):
                            logger.info(f"Processing page {i + 1} of {max_pages}")
                            img_byte_arr = BytesIO()
                            images[i].save(img_byte_arr, format="PNG")
                            page_image_bytes = img_byte_arr.getvalue()
                            
                            # Extract text from this page
                            page_text = self._extract_single_image(page_image_bytes, "image/png", f"Page {i + 1}")
                            if page_text and not page_text.startswith("Error"):
                                extracted_texts.append(f"--- Page {i + 1} ---\n{page_text}")
                        
                        # Combine all page texts
                        if extracted_texts:
                            return "\n\n".join(extracted_texts)
                        else:
                            logger.error("No text extracted from any PDF pages")
                            return "Error extracting text: No text found in PDF"
                    else:
                        logger.warning("pdf2image conversion resulted in no images.")
                        # Try raw PDF as fallback
                        return self._extract_single_image(file_content, "application/pdf", file_name)
                except Exception as e:
                    logger.error(f"Error converting PDF to images: {str(e)}", exc_info=True)
                    # Fallback: try sending raw PDF bytes
                    return self._extract_single_image(file_content, "application/pdf", file_name)
            else:
                logger.warning("pdf2image is not installed. Attempting raw PDF processing.")
                return self._extract_single_image(file_content, "application/pdf", file_name)
                
        elif doc_type == DocumentType.IMAGE:
            # Determine MIME type for images
            mime_type = DOC_TYPE_TO_MIME.get(doc_type, "image/jpeg")
            if file_name:
                ext = file_name.split(".")[-1].lower()
                if ext == "png":
                    mime_type = "image/png"
                elif ext in ["jpg", "jpeg"]:
                    mime_type = "image/jpeg"
            return self._extract_single_image(file_content, mime_type, file_name)
            
        else:
            # For other document types
            mime_type = DOC_TYPE_TO_MIME.get(doc_type, "application/octet-stream")
            return self._extract_single_image(file_content, mime_type, file_name)
    
    def _extract_single_image(self, image_bytes: bytes, mime_type: str, context: str) -> str:
        """Extract text from a single image/page using Gemini."""
        if not image_bytes:
            return "Error extracting text: No content provided"
            
        try:
            file_base64 = base64.b64encode(image_bytes).decode("utf-8")
            logger.info(f"Extracting from {context} with MIME type: {mime_type}")
            data_uri = f"data:{mime_type};base64,{file_base64}"

            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Extract ALL text from this image or document. Include every detail, paragraph, section, header, footer, and any visible text. Be thorough and complete.",
                        },
                        {"type": "image_url", "image_url": {"url": data_uri}},
                    ],
                }
            ]

            logger.info(f"Invoking Gemini model for {context}")
            response = self.llm.invoke(messages)
            
            if hasattr(response, "content"):
                return str(response.content)
            else:
                return str(response)
        except Exception as e:
            error_message = f"Error extracting text from {context}: {str(e)}"
            logger.error(error_message, exc_info=True)
            return error_message


class DocumentProcessor:
    """Service to handle document processing for various file types."""

    def __init__(
        self,
        config_service: Optional[LLMConfigService] = None,
        chain_factory: Optional[LLMChainFactory] = None,
    ):
        # Use provided config service or create one
        self.config_service = config_service or LLMConfigService()
        self.chain_factory = chain_factory

        # Get chunking parameters from config
        chunking_params = self.config_service.get_chunking_params(LLMTask.DOCUMENT_EXTRACTION)

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunking_params["chunk_size"],
            chunk_overlap=chunking_params["chunk_overlap"],
            length_function=len,
        )

        # Initialize default text extractor with flash model for speed
        self.default_provider = DEFAULT_PROVIDER
        self.default_model = DEFAULT_MODEL

        # Keep direct genai reference for backward compatibility with tests
        self.gemini_model = genai.GenerativeModel(DEFAULT_MODEL.value)

        # Initialize LangChain-based text extractor
        self.text_extractor = GeminiTextExtractor(DEFAULT_MODEL.value)

        logger.info(f"Initialized DocumentProcessor with default model: {DEFAULT_MODEL.value}")

    def _get_text_extractor(self, model_config: Optional[Dict[str, Any]] = None) -> LLMTextExtractor:
        """Get a text extractor based on configuration."""
        # Get model configuration
        if model_config and "model" in model_config:
            model_name = model_config["model"]
        else:
            model_name = DEFAULT_MODEL.value

        # Create a basic Gemini extractor without tracing
        return GeminiTextExtractor(model_name=model_name)

    async def _get_text_extractor_with_tracing(
        self,
        model_name: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> GeminiTextExtractor:
        """Get a text extractor with tracing enabled."""
        if not self.chain_factory:
            # Create a basic Gemini extractor without tracing
            return GeminiTextExtractor(model_name=model_name)

        # Create LLM with tracing
        llm = self.chain_factory.create_llm(
            model_name=model_name,
            provider=LLMProvider.GOOGLE.value,
            temperature=0.0,
            user_id=user_id,
            session_id=session_id,
            tags=["task:document_extraction"],
        )

        # Extract callbacks from LLM
        callbacks: Optional[List[BaseCallbackHandler]] = None
        if hasattr(llm, "callbacks"):
            if isinstance(llm.callbacks, list):
                callbacks = [cb for cb in llm.callbacks if isinstance(cb, BaseCallbackHandler)]
            elif isinstance(llm.callbacks, BaseCallbackManager):
                callbacks = [cb for cb in llm.callbacks.handlers if isinstance(cb, BaseCallbackHandler)]

        # Create a new Gemini instance with the callbacks
        gemini_llm = ChatGoogleGenerativeAI(
            model=model_name,
            temperature=0.0,
            convert_system_message_to_human=True,
            callbacks=callbacks,
        )

        # Create extractor with the LLM
        return GeminiTextExtractor(
            model_name=model_name,
            callbacks=callbacks,
            llm=gemini_llm,
        )

    async def process_file_with_tracing(
        self,
        file_content: bytes,
        file_name: str,
        user_id: Optional[str] = None,
        model_config: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """Process a file with tracing enabled."""
        # Get model configuration
        if model_config and "model" in model_config:
            model_name = model_config["model"]
        else:
            model_name = DEFAULT_MODEL.value

        # Get text extractor with tracing
        text_extractor = await self._get_text_extractor_with_tracing(
            model_name=model_name,
            user_id=user_id,
        )

        # Get document type from file extension
        file_extension = self._get_file_extension(file_name)
        doc_type = self._get_doc_type(file_extension)

        # Process with LLM
        return self._process_with_llm(file_content, file_name, doc_type, text_extractor)

    def process_file(
        self,
        file_content: bytes,
        file_name: str,
        model_config: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """
        Process a file and split it into chunks suitable for embedding.

        Args:
            file_content: The binary content of the file
            file_name: The name of the file
            model_config: Optional configuration for the LLM model
        """
        file_extension = file_name.split(".")[-1].lower()
        doc_type = self._get_doc_type(file_extension)

        # Configure text extractor based on model_config
        text_extractor = self._get_text_extractor(model_config)

        # Use LLM for text extraction for all document types
        logger.info(f"Using LLM for {doc_type.value} processing with model: {text_extractor.model_name}")
        return self._process_with_llm(file_content, file_name, doc_type, text_extractor)

    def _process_with_llm(
        self,
        file_content: bytes,
        file_name: str,
        doc_type: DocumentType,
        text_extractor: LLMTextExtractor,
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """Process document using LLM for text extraction."""
        try:
            # Extract text using LLM
            logger.info(
                f"Extracting text with LLM: {text_extractor.__class__.__name__}, model: {text_extractor.model_name}"
            )
            extracted_text = text_extractor.extract_text(file_content, file_name, doc_type)
            logger.info(f"Extracted text length: {len(extracted_text)}")
            logger.info(f"Extracted text preview: {extracted_text[:200]}...")

            # Check if we got an error message back
            if extracted_text.startswith("Error extracting text:"):
                logger.warning(f"Extraction contained error message: {extracted_text}")
                # Try with traditional methods as fallback
                logger.info("Falling back to traditional methods due to extraction error...")
                return self._process_with_traditional_methods(file_content, file_name)

            # Create document and metadata
            document = Document(
                page_content=extracted_text,
                metadata={
                    "doc_type": doc_type,
                    "extraction_method": "llm",
                    "file_name": file_name,
                },
            )

            # Split into chunks
            logger.info("Splitting document into chunks...")
            split_docs = self.text_splitter.split_documents([document])
            logger.info(f"Split into {len(split_docs)} chunks")

            return split_docs, document.metadata

        except Exception as e:
            logger.error(f"Error processing document with LLM: {str(e)}", exc_info=True)
            # Fallback to traditional methods if LLM fails
            logger.info("Falling back to traditional methods...")
            return self._process_with_traditional_methods(file_content, file_name)

    def _process_with_traditional_methods(
        self, file_content: bytes, file_name: str
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """Process document using traditional methods."""
        try:
            # Save file temporarily
            temp_path = self._save_temp_file(file_content, self._get_file_extension(file_name))
            logger.info(f"Saved temporary file: {temp_path}")

            # Get document type
            doc_type = self._get_doc_type(self._get_file_extension(file_name))

            # Choose appropriate loader based on file type
            if doc_type == DocumentType.DOCX:
                docx_loader = Docx2txtLoader(temp_path)
                docs = docx_loader.load()
            elif doc_type == DocumentType.TXT:
                txt_loader = TextLoader(temp_path)
                docs = txt_loader.load()
            elif doc_type == DocumentType.PDF:
                pdf_loader = PyPDFLoader(temp_path)
                docs = pdf_loader.load()
            else:
                unstructured_loader = UnstructuredFileLoader(temp_path)
                docs = unstructured_loader.load()

            logger.info(f"Loaded document with {len(docs)} pages")

            # Split into chunks
            split_docs = self.text_splitter.split_documents(docs)
            logger.info(f"Split into {len(split_docs)} chunks")

            # Clean up temp file
            os.remove(temp_path)

            return split_docs, {
                "doc_type": doc_type,
                "extraction_method": "traditional",
                "file_name": file_name,
            }

        except Exception as e:
            logger.error(f"Error processing document: {str(e)}", exc_info=True)
            raise

    def _get_doc_type(self, file_extension: str) -> DocumentType:
        """Get the document type enum value based on file extension."""
        return EXTENSION_TO_DOC_TYPE.get(file_extension.lower(), DocumentType.TXT)

    def process_text(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> List[Document]:
        """Process raw text and split into chunks."""
        if metadata is None:
            metadata = {}

        documents = [Document(page_content=text, metadata=metadata)]
        return self.text_splitter.split_documents(documents)

    async def process_text_with_tracing(
        self,
        text: str,
        metadata: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ) -> List[Document]:
        """
        Process raw text and split into chunks with tracing.

        This async version enables tracing when chain_factory is available.
        """
        if metadata is None:
            metadata = {}

        # Add tracing info to metadata for visibility
        if user_id:
            metadata["user_id"] = user_id
        metadata["tracing_enabled"] = self.chain_factory is not None

        documents = [Document(page_content=text, metadata=metadata)]
        return self.text_splitter.split_documents(documents)

    def _get_file_extension(self, filename: str) -> str:
        """Extract file extension from filename."""
        return filename.split(".")[-1]

    def _save_temp_file(self, content: bytes, extension: str) -> str:
        """Save content to a temporary file and return the file path."""
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)

        temp_file_path = os.path.join(temp_dir, f"{uuid.uuid4()}.{extension}")
        with open(temp_file_path, "wb") as f:
            f.write(content)

        return temp_file_path
