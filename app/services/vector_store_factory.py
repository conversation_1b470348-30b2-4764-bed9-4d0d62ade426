import os
from typing import Optional, <PERSON>

from dotenv import load_dotenv
from langchain.embeddings.base import Embeddings

from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.vector_store import VectorStoreService

load_dotenv()


def create_vector_store(
    embedding_model: Optional[Embeddings] = None,
) -> Union[VectorStoreService, PostgresVectorStoreService]:
    """Factory function to create the appropriate vector store based on environment configuration.

    Returns either a ChromaVectorStore or PostgresVectorStore based on the VECTOR_STORE_TYPE env var.
    """
    vector_store_type = os.getenv("VECTOR_STORE_TYPE", "chroma").lower()

    if vector_store_type == "postgres":
        # Check if we have a database URL configured
        if not os.getenv("DATABASE_URL"):
            raise ValueError("PostgreSQL vector store selected but DATABASE_URL environment variable is not set")

        return PostgresVectorStoreService(embedding_model=embedding_model)
    else:
        # Default to Chroma
        return VectorStoreService(embedding_model=embedding_model)
