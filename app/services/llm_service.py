import json
from typing import Any, Dict, <PERSON>, Optional, Tuple, TypeVar, Union

from dotenv import load_dotenv
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import (
    RunnablePassthrough,
    RunnableSequence,
    RunnableLambda,
    RunnableSerializable,
)

from app.schemas.lawsuit_schema import (
    Customer,
    LawsuitData,
    Organization,
)
from app.schemas.llm_config_schema import LLMTask
from app.services.llm_config_service import LLMConfigService
from app.services.vector_store import VectorStoreService
from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.llm_chain_factory import LLMChainFactory

load_dotenv()

# Define type aliases for runnables
RunnableType = TypeVar("RunnableType", bound=Union[RunnableSequence, RunnableSerializable])

# Type for vector stores - can be either service implementation
VectorStore = Union[VectorStoreService, PostgresVectorStoreService]


class LLMService:
    """Service to handle LLM operations for RAG and data extraction."""

    def __init__(
        self,
        vector_store_service: VectorStore,
        config_service: LLMConfigService,
        chain_factory: Optional[LLMChainFactory] = None,
    ):
        self.vector_store = vector_store_service
        self.config_service = config_service
        self.chain_factory = chain_factory

        # Initialize prompt templates
        self._init_qa_prompt()
        self._init_lawsuit_extraction_prompt()

    def _init_qa_prompt(self) -> None:
        """Initialize the QA prompt template."""
        self.qa_prompt = ChatPromptTemplate.from_template(
            """
        You are a helpful AI assistant tasked with answering questions based on the provided documents.

        Context:
        {context}

        Question:
        {question}

        Instructions:
        1. Provide a clear, concise, and accurate answer based on the context.
        2. If the context doesn't contain enough information to answer the question,
           say "I don't have enough information to answer this question."
        3. Do not make up information or use knowledge outside of the provided context.

        Answer:
        """
        )

    def _init_lawsuit_extraction_prompt(self) -> None:
        """Initialize the lawsuit data extraction prompt template."""
        self.lawsuit_extraction_prompt = ChatPromptTemplate.from_template(
            """
        You are a legal AI assistant that specializes in extracting structured data from legal documents.

        Context from documents:
        {context}

        Optional configuration:
        {config}

        Your task is to extract the following information to create a structured JSON object:
        - is_prelit: Boolean indicating if pre-lit (if the document has a court defined or
          is a Summons and Complaint, set this to false)
        - lawsuit_exists: Boolean indicating if a lawsuit exists
        - lawsuit_state: The U.S. state where the lawsuit is filed
        - lawsuit_amount: The amount of the lawsuit as a string
        - civil_number: The civil case number
        - lawyer_email: The email of the lawyer
        - law_firm: An object with id (integer) and name (string)
        - plaintiff: An object with id (integer) and name (string)
        - customer: An object with name, phone_number, and mailing_address
          (which has customer_street, customer_city, customer_state, customer_zip_code)

        If any information is missing, you can omit that field entirely.
        ALL FIELDS ARE OPTIONAL - only include fields where you have information.
        Do not make up values or use placeholders - it's better to omit a field than to guess.

        IMPORTANT: The output must be valid JSON:
        1. DO NOT include any comments in the JSON (no // or /* */ style comments)
        2. DO NOT include any explanatory text as part of the JSON
        3. DO NOT include uncertainty indicators in the JSON values
        4. If you're uncertain about a value, use "Unknown" or a reasonable default
        5. Follow the exact structure below with no additional fields

        The output must strictly follow this JSON structure with no additional text:
        {{
          "is_prelit": boolean,
          "lawsuit_exists": boolean,
          "lawsuit_state": one of the 50 U.S. states (spelled out, not abbreviated),
          "lawsuit_amount": string,
          "civil_number": string,
          "lawyer_email": valid email format (leave empty or null if not found),
          "law_firm": {{
            "id": integer,
            "name": string
          }},
          "plaintiff": {{
            "id": integer,
            "name": string
          }},
          "customer": {{
            "name": string,
            "phone_number": string,
            "mailing_address": {{
              "customer_street": string,
              "customer_city": string,
              "customer_state": string,
              "customer_zip_code": string
            }}
          }}
        }}
        """
        )

    async def answer_question(
        self,
        question: str,
        user_id: str,
        requested_provider: Optional[str] = None,
        requested_model: Optional[str] = None,
    ) -> Tuple[str, List[str]]:
        """
        Answer a question using the RAG approach with the specified LLM.

        Args:
            question: The question to answer
            user_id: The ID of the user whose documents to search
            requested_provider: Which LLM provider to use (optional)
            requested_model: Which model to use (optional)

        Returns:
            Tuple containing the answer and a list of source document IDs
        """
        # Resolve which provider and model to use
        provider, model_name = self.config_service.resolve_provider_model(
            LLMTask.QUESTION_ANSWERING, requested_provider, requested_model
        )

        # Get retrieval params for question answering
        retrieval_params = self.config_service.get_retrieval_params(LLMTask.QUESTION_ANSWERING)

        # Retrieve relevant documents for this specific user
        docs = self.vector_store.search_documents(question, user_id, k=retrieval_params.get("k", 4))

        if not docs:
            return (
                "I don't have any documents to reference for answering this question.",
                [],
            )

        # Format documents for the context
        context = "\n\n".join([doc.page_content for doc in docs])

        # Get source document IDs
        source_ids = list(set([doc.metadata.get("document_id", "unknown") for doc in docs]))

        # Get inference parameters for this task
        inference_params = self.config_service.get_inference_params(LLMTask.QUESTION_ANSWERING)
        temperature = inference_params.get("temperature", 0.0)

        # Create QA chain using the chain factory if available, otherwise use direct approach
        if self.chain_factory:
            # Use chain factory with tracing
            qa_chain = await self._create_qa_chain_with_factory(provider, model_name, temperature, user_id, context)
            answer = await qa_chain.ainvoke(question)
        else:
            # Get the model instance directly (legacy approach)
            llm = self.config_service.get_model_instance(provider, model_name)

            # Create and run the QA chain
            run_context = RunnableLambda(lambda _: context)
            qa_chain = (
                {"context": run_context, "question": RunnablePassthrough()} | self.qa_prompt | llm | StrOutputParser()
            )
            answer = qa_chain.invoke(question)

        return answer, source_ids

    async def _create_qa_chain_with_factory(
        self,
        provider: str,
        model_name: str,
        temperature: float,
        user_id: str,
        context: str,
    ) -> RunnableSerializable:
        """Create a QA chain using the chain factory with tracing."""

        # Create a runnable that will insert the context
        def add_context(input_dict):
            return {"context": context, "question": input_dict}

        # Get a model with our callback handler for tracing
        llm = self.chain_factory.create_llm(
            model_name=model_name,
            provider=provider,
            temperature=temperature,
            user_id=user_id,
            tags=["task:question_answering"],
        )

        # Create the chain
        chain = RunnableLambda(add_context) | self.qa_prompt | llm | StrOutputParser()

        return chain

    async def extract_lawsuit_data(self, user_id: str, config: Optional[Dict[str, Any]] = None) -> LawsuitData:
        """
        Extract lawsuit data from the user's documents in the vector store.

        Args:
            user_id: The ID of the user whose documents to analyze
            config: Optional configuration to guide the extraction

        Returns:
            LawsuitData object
        """
        # Get requested provider and model if specified in config
        requested_provider = config.get("provider") if config else None
        requested_model = config.get("model") if config else None

        # Resolve which provider and model to use
        provider, model_name = self.config_service.resolve_provider_model(
            LLMTask.LAWSUIT_EXTRACTION, requested_provider, requested_model
        )

        # Get retrieval params for lawsuit extraction
        retrieval_params = self.config_service.get_retrieval_params(LLMTask.LAWSUIT_EXTRACTION)

        # Use a more comprehensive retrieval to get a broad context
        docs = self.vector_store.search_documents(
            "lawsuit case plaintiff defendant customer details amount",
            user_id,
            k=retrieval_params.get("k", 10),
        )

        if not docs:
            # Return empty LawsuitData object
            return LawsuitData()

        # Format documents for the context
        context = "\n\n".join([doc.page_content for doc in docs])

        # Format the config as a string
        config_str = json.dumps(config) if config else "No additional configuration provided."

        # Get inference parameters
        inference_params = self.config_service.get_inference_params(LLMTask.LAWSUIT_EXTRACTION)
        temperature = inference_params.get("temperature", 0.0)

        # Use the chain factory if available
        if self.chain_factory:
            # Create extraction chain with tracing
            json_parser = JsonOutputParser()

            # Create a chain that will extract lawsuit data
            extraction_chain = await self._create_extraction_chain_with_factory(
                provider,
                model_name,
                temperature,
                user_id,
                context,
                config_str,
                json_parser,
            )

            # Run the extraction chain
            result = await extraction_chain.ainvoke({})
        else:
            # Use the legacy approach
            llm = self.config_service.get_model_instance(provider, model_name)
            json_parser = JsonOutputParser()
            run_context = RunnableLambda(lambda _: context)
            run_config = RunnableLambda(lambda _: config_str)

            extraction_chain = (
                {"context": run_context, "config": run_config} | self.lawsuit_extraction_prompt | llm | json_parser
            )

            # Run the extraction chain
            result = extraction_chain.invoke({})

        # Convert the result to a LawsuitData object
        lawsuit_data = LawsuitData(
            lawsuit_exists=result.get("lawsuit_exists"),
            lawsuit_state=result.get("lawsuit_state"),
            lawsuit_amount=result.get("lawsuit_amount"),
            civil_number=result.get("civil_number"),
            lawyer_email=result.get("lawyer_email"),
            law_firm=(Organization(**result.get("law_firm", {})) if result.get("law_firm") else None),
            plaintiff=(Organization(**result.get("plaintiff", {})) if result.get("plaintiff") else None),
            customer=(Customer(**result.get("customer", {})) if result.get("customer") else None),
        )

        return lawsuit_data

    async def _create_extraction_chain_with_factory(
        self,
        provider: str,
        model_name: str,
        temperature: float,
        user_id: str,
        context: str,
        config_str: str,
        json_parser: JsonOutputParser,
    ) -> RunnableSerializable:
        """Create an extraction chain using the chain factory with tracing."""

        # Create a runnable that will insert context and config
        def add_inputs(input_dict):
            return {"context": context, "config": config_str}

        # Get a model with our callback handler for tracing
        llm = self.chain_factory.create_llm(
            model_name=model_name,
            provider=provider,
            temperature=temperature,
            user_id=user_id,
            tags=["task:lawsuit_extraction"],
        )

        # Create the chain
        chain = RunnableLambda(add_inputs) | self.lawsuit_extraction_prompt | llm | json_parser

        return chain
