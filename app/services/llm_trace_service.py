import os
import uuid
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, UTC

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import select, insert, delete

from app.schemas.llm_trace_schema import (
    LLMTraceCreate,
    LLMTrace,
    LLMTraceBatch,
    LLMTraceFilter,
)

logger = logging.getLogger(__name__)

# Define the database table if it doesn't exist
metadata = sa.MetaData()

llm_traces = sa.Table(
    "llm_traces",
    metadata,
    sa.Column("id", UUID, primary_key=True, default=uuid.uuid4),
    sa.Column("user_id", sa.String, nullable=True, index=True),
    sa.Column("session_id", sa.String, nullable=True, index=True),
    sa.Column("request_id", sa.String, nullable=True, index=True),
    sa.Column("provider", sa.String, nullable=False, index=True),
    sa.Column("model", sa.String, nullable=False, index=True),
    sa.Column("prompt", JSONB, nullable=False),
    sa.Column("prompt_tokens", sa.Integer, nullable=True),
    sa.Column("response", JSONB, nullable=True),
    sa.Column("completion_tokens", sa.Integer, nullable=True),
    sa.Column("total_tokens", sa.Integer, nullable=True),
    sa.Column("status", sa.String, nullable=False, index=True),
    sa.Column("error_message", sa.Text, nullable=True),
    sa.Column("duration_ms", sa.Integer, nullable=True),
    sa.Column("parameters", JSONB, nullable=True),
    sa.Column("metadata", JSONB, nullable=True),
    sa.Column("tags", sa.ARRAY(sa.String), nullable=True, index=True),
    sa.Column(
        "created_at",
        sa.DateTime,
        default=lambda: datetime.now(UTC).replace(tzinfo=None),
        nullable=False,
        index=True,
    ),
    sa.Column(
        "updated_at",
        sa.DateTime,
        nullable=True,
        onupdate=lambda: datetime.now(UTC).replace(tzinfo=None),
    ),
)


class LLMTraceService:
    """Service for storing and retrieving LLM traces."""

    def __init__(self, postgres_url: Optional[str] = None):
        """Initialize the LLM trace service with PostgreSQL connection."""
        # Get connection details from provided URL or environment
        self.postgres_url = postgres_url or os.getenv("DATABASE_URL")

        if not self.postgres_url:
            raise ValueError("PostgreSQL URL is required for LLMTraceService")

        # Handle Heroku-style postgres:// URLs by converting to postgresql://
        if self.postgres_url.startswith("postgres://"):
            self.postgres_url = self.postgres_url.replace("postgres://", "postgresql://", 1)
            logger.info("Converted Heroku postgres:// URL to postgresql:// format")

        # Ensure the URL has the asyncpg driver for SQLAlchemy
        if "+asyncpg" not in self.postgres_url and "postgresql://" in self.postgres_url:
            self.postgres_url = self.postgres_url.replace("postgresql://", "postgresql+asyncpg://", 1)
            logger.info("Added asyncpg driver to PostgreSQL URL for SQLAlchemy compatibility")

        # Determine environment
        env = os.getenv("ENVIRONMENT", "development").lower()

        # Check if we need to modify the database name based on environment
        # Only do this for local development, not on Heroku
        is_heroku = "HEROKU_APP_NAME" in os.environ or env in ["staging", "production"]

        if not postgres_url and not is_heroku:  # Only auto-adjust if using environment variable in local development
            import re

            # Determine the appropriate database based on environment
            if env == "production":
                db_name = "solocircuit_prod"
            elif env == "staging":
                db_name = "solocircuit_staging"
            else:  # development or test
                db_name = "solocircuit_test"

            # Extract and replace the database name in the URL
            url_pattern = r"(postgresql(?:\+\w+)?:\/\/[^\/]+\/)([^?]+)(.*?)"
            match = re.match(url_pattern, self.postgres_url)

            if match:
                self.postgres_url = f"{match.group(1)}{db_name}{match.group(3) or ''}"
                logger.info(f"Using database: {db_name}")
        elif is_heroku:
            logger.info("Using Heroku-provided database")

        # Create an async engine for PostgreSQL
        self.engine = create_async_engine(  # type: ignore
            self.postgres_url,
            echo=False,
            json_serializer=lambda obj: json.dumps(obj, default=str),
        )

        # Create session factory
        self.async_session = sessionmaker(bind=self.engine, expire_on_commit=False, class_=AsyncSession)  # type: ignore

        logger.info("Initialized LLMTraceService with PostgreSQL connection")

    async def initialize(self) -> None:
        """Initialize the database tables if they don't exist."""
        async with self.engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
            logger.info("Created LLM trace tables if they didn't exist")

    async def create_trace(self, trace: LLMTraceCreate) -> LLMTrace:
        """Create a new LLM trace in the database."""
        trace_id = str(uuid.uuid4())

        try:
            # Convert prompt and response to JSON-serializable format
            trace_data = trace.model_dump()

            # Log the trace creation
            logger.info(
                f"Creating trace: provider={trace_data.get('provider')}, "
                f"model={trace_data.get('model')}, "
                f"status={trace_data.get('status')}, "
                f"user_id={trace_data.get('user_id')}, "
                f"request_id={trace_data.get('request_id')}"
            )

            # Ensure request_id is a string
            if trace_data.get("request_id") and not isinstance(trace_data["request_id"], str):
                trace_data["request_id"] = str(trace_data["request_id"])
                logger.debug(f"Converted request_id to string: {trace_data['request_id']}")

            # Preserve original prompt for tracing

            # Serialize prompt if it's not already a string
            if isinstance(trace_data["prompt"], list) or isinstance(trace_data["prompt"], dict):
                try:
                    trace_data["prompt"] = json.dumps(trace_data["prompt"])
                except Exception as e:
                    logger.error(f"Error serializing prompt to JSON: {str(e)}")
                    # Fallback to string representation
                    trace_data["prompt"] = str(trace_data["prompt"])

            # Preserve original response for tracing

            # Serialize response if present and not already a string
            if trace_data.get("response") and (
                isinstance(trace_data["response"], dict) or isinstance(trace_data["response"], list)
            ):
                try:
                    trace_data["response"] = json.dumps(trace_data["response"])
                except Exception as e:
                    logger.error(f"Error serializing response to JSON: {str(e)}")
                    # Fallback to string representation
                    trace_data["response"] = str(trace_data["response"])

            # Remove timezone info for database compatibility
            created_time = datetime.now(UTC).replace(tzinfo=None)

            # Prepare the insert query
            query = insert(llm_traces).values(
                id=trace_id,
                **trace_data,
                created_at=created_time,
            )

            async with self.async_session() as session:
                async with session.begin():
                    try:
                        # Insert main trace record
                        await session.execute(query)
                        await session.commit()
                        logger.debug(f"Trace {trace_id} created successfully")
                    except Exception as db_error:
                        logger.error(f"Database error saving trace: {str(db_error)}")
                        raise

            # Return the created trace with timezone-aware datetime
            return LLMTrace(
                id=trace_id,
                created_at=datetime.now(UTC),
                user_id=trace.user_id,
                session_id=trace.session_id,
                request_id=trace.request_id,
                provider=trace.provider,
                model=trace.model,
                prompt=trace.prompt,
                prompt_tokens=trace.prompt_tokens,
                response=trace.response,
                completion_tokens=trace.completion_tokens,
                total_tokens=trace.total_tokens,
                status=trace.status,
                error_message=trace.error_message,
                duration_ms=trace.duration_ms,
                parameters=trace.parameters,
                metadata=trace.metadata,
                tags=trace.tags,
                updated_at=datetime.now(UTC),
            )

        except Exception as e:
            logger.error(f"Error creating trace: {str(e)}")
            # Return a basic trace object with the error
            return LLMTrace(
                id=trace_id,
                created_at=datetime.now(UTC),
                user_id=None,
                session_id=None,
                request_id=None,
                provider=trace.provider,
                model=trace.model,
                prompt="Error creating trace",
                prompt_tokens=0,
                response=None,
                completion_tokens=0,
                total_tokens=0,
                status=trace.status,
                error_message=f"Error creating trace: {str(e)}",
                duration_ms=0,
                parameters={},
                metadata={},
                tags=[],
                updated_at=datetime.now(UTC),
            )

    async def create_traces_batch(self, batch: LLMTraceBatch) -> List[str]:
        """Create multiple LLM traces in the database in a single transaction."""
        trace_ids = []

        async with self.async_session() as session:
            async with session.begin():
                for trace in batch.traces:
                    trace_id = str(uuid.uuid4())
                    trace_ids.append(trace_id)

                    # Convert trace to dict and prepare data
                    trace_data = trace.model_dump()

                    # Serialize prompt if it's not already a string
                    if isinstance(trace_data["prompt"], list) or isinstance(trace_data["prompt"], dict):
                        trace_data["prompt"] = json.dumps(trace_data["prompt"])

                    # Serialize response if present and not already a string
                    if trace_data.get("response") and (
                        isinstance(trace_data["response"], dict) or isinstance(trace_data["response"], list)
                    ):
                        trace_data["response"] = json.dumps(trace_data["response"])

                    # Remove timezone info for database compatibility
                    created_time = datetime.now(UTC).replace(tzinfo=None)

                    # Execute insert
                    query = insert(llm_traces).values(
                        id=trace_id,
                        **trace_data,
                        created_at=created_time,
                    )
                    await session.execute(query)

                await session.commit()

        return trace_ids

    async def get_trace(self, trace_id: str) -> Optional[LLMTrace]:
        """Get an LLM trace by its ID."""
        query = select(llm_traces).where(llm_traces.c.id == trace_id)

        async with self.async_session() as session:
            result = await session.execute(query)
            trace_row = result.fetchone()

            if not trace_row:
                return None

            # Convert row to dict
            trace_dict = {col.name: getattr(trace_row, col.name) for col in llm_traces.columns}

            # Convert UUID to string for id column (if it's a UUID object)
            if isinstance(trace_dict["id"], uuid.UUID):
                trace_dict["id"] = str(trace_dict["id"])

            # Parse JSON fields
            if isinstance(trace_dict["prompt"], str):
                try:
                    trace_dict["prompt"] = json.loads(trace_dict["prompt"])
                except json.JSONDecodeError:
                    pass  # Keep as string if not valid JSON

            if trace_dict.get("response") and isinstance(trace_dict["response"], str):
                try:
                    trace_dict["response"] = json.loads(trace_dict["response"])
                except json.JSONDecodeError:
                    pass  # Keep as string if not valid JSON

            return LLMTrace(**trace_dict)

    async def search_traces(
        self, filter_params: LLMTraceFilter, limit: int = 100, offset: int = 0
    ) -> Tuple[List[LLMTrace], int]:
        """
        Search for LLM traces based on filter parameters.
        Returns a tuple of (traces, total_count).
        """
        query = select(llm_traces)
        count_query = select(sa.func.count()).select_from(llm_traces)

        # Apply filters
        if filter_params.user_id:
            query = query.where(llm_traces.c.user_id == filter_params.user_id)
            count_query = count_query.where(llm_traces.c.user_id == filter_params.user_id)

        if filter_params.session_id:
            query = query.where(llm_traces.c.session_id == filter_params.session_id)
            count_query = count_query.where(llm_traces.c.session_id == filter_params.session_id)

        if filter_params.request_id:
            query = query.where(llm_traces.c.request_id == filter_params.request_id)
            count_query = count_query.where(llm_traces.c.request_id == filter_params.request_id)

        if filter_params.provider:
            query = query.where(llm_traces.c.provider == filter_params.provider)
            count_query = count_query.where(llm_traces.c.provider == filter_params.provider)

        if filter_params.model:
            query = query.where(llm_traces.c.model == filter_params.model)
            count_query = count_query.where(llm_traces.c.model == filter_params.model)

        if filter_params.status:
            query = query.where(llm_traces.c.status == filter_params.status.value)
            count_query = count_query.where(llm_traces.c.status == filter_params.status.value)

        if filter_params.start_date:
            query = query.where(llm_traces.c.created_at >= filter_params.start_date)
            count_query = count_query.where(llm_traces.c.created_at >= filter_params.start_date)

        if filter_params.end_date:
            query = query.where(llm_traces.c.created_at <= filter_params.end_date)
            count_query = count_query.where(llm_traces.c.created_at <= filter_params.end_date)

        if filter_params.tags:
            # Use PostgreSQL's ANY operator instead of overlap
            # This matches rows where ANY of the tags in the filter match ANY of the tags in the row
            for tag in filter_params.tags:
                query = query.where(tag == sa.any_(llm_traces.c.tags))  # type: ignore
                count_query = count_query.where(tag == sa.any_(llm_traces.c.tags))  # type: ignore

        # Apply pagination
        query = query.order_by(llm_traces.c.created_at.desc()).limit(limit).offset(offset)

        async with self.async_session() as session:
            # Get total count
            count_result = await session.execute(count_query)
            total_count = count_result.scalar() or 0

            # Get paginated results
            result = await session.execute(query)
            trace_rows = result.fetchall()

            traces = []
            for row in trace_rows:
                # Convert row to dict
                trace_dict = {col.name: getattr(row, col.name) for col in llm_traces.columns}

                # Convert UUID to string for id column (if it's a UUID object)
                if isinstance(trace_dict["id"], uuid.UUID):
                    trace_dict["id"] = str(trace_dict["id"])

                # Parse JSON fields
                if isinstance(trace_dict["prompt"], str):
                    try:
                        trace_dict["prompt"] = json.loads(trace_dict["prompt"])
                    except json.JSONDecodeError:
                        pass  # Keep as string if not valid JSON

                if trace_dict.get("response") and isinstance(trace_dict["response"], str):
                    try:
                        trace_dict["response"] = json.loads(trace_dict["response"])
                    except json.JSONDecodeError:
                        pass  # Keep as string if not valid JSON

                traces.append(LLMTrace(**trace_dict))

            return traces, total_count

    async def get_stats(self, filter_params: LLMTraceFilter, time_interval: str = "day") -> Dict[str, Any]:
        """
        Get statistics about LLM usage.
        time_interval can be 'hour', 'day', 'week', or 'month'.
        """
        interval_map = {
            "hour": sa.func.date_trunc("hour", llm_traces.c.created_at),
            "day": sa.func.date_trunc("day", llm_traces.c.created_at),
            "week": sa.func.date_trunc("week", llm_traces.c.created_at),
            "month": sa.func.date_trunc("month", llm_traces.c.created_at),
        }

        interval_func = interval_map.get(time_interval, interval_map["day"])

        # Base query conditions
        conditions = []

        if filter_params.user_id:
            conditions.append(llm_traces.c.user_id == filter_params.user_id)

        if filter_params.session_id:
            conditions.append(llm_traces.c.session_id == filter_params.session_id)

        if filter_params.provider:
            conditions.append(llm_traces.c.provider == filter_params.provider)

        if filter_params.model:
            conditions.append(llm_traces.c.model == filter_params.model)

        if filter_params.status:
            conditions.append(llm_traces.c.status == filter_params.status.value)

        if filter_params.start_date:
            conditions.append(llm_traces.c.created_at >= filter_params.start_date)

        if filter_params.end_date:
            conditions.append(llm_traces.c.created_at <= filter_params.end_date)

        if filter_params.tags:
            # Use ANY operator for tags filtering
            for tag in filter_params.tags:
                conditions.append(tag == sa.any_(llm_traces.c.tags))  # type: ignore

        # Prepare queries
        time_query = select(
            interval_func.label("time_period"),
            sa.func.count().label("count"),
            sa.func.sum(llm_traces.c.total_tokens).label("total_tokens"),
            sa.func.avg(llm_traces.c.duration_ms).label("avg_duration_ms"),
        )
        if conditions:
            time_query = time_query.where(sa.and_(*conditions))  # type: ignore
        time_query = time_query.group_by("time_period").order_by("time_period")

        model_query = select(
            llm_traces.c.model,
            sa.func.count().label("count"),
            sa.func.sum(llm_traces.c.total_tokens).label("total_tokens"),
            sa.func.avg(llm_traces.c.duration_ms).label("avg_duration_ms"),
        )
        if conditions:
            model_query = model_query.where(sa.and_(*conditions))  # type: ignore
        model_query = model_query.group_by(llm_traces.c.model).order_by(sa.desc("count"))

        provider_query = select(
            llm_traces.c.provider,
            sa.func.count().label("count"),
            sa.func.sum(llm_traces.c.total_tokens).label("total_tokens"),
            sa.func.avg(llm_traces.c.duration_ms).label("avg_duration_ms"),
        )
        if conditions:
            provider_query = provider_query.where(sa.and_(*conditions))  # type: ignore
        provider_query = provider_query.group_by(llm_traces.c.provider).order_by(sa.desc("count"))

        status_query = select(
            llm_traces.c.status,
            sa.func.count().label("count"),
        )
        if conditions:
            status_query = status_query.where(sa.and_(*conditions))  # type: ignore
        status_query = status_query.group_by(llm_traces.c.status)

        async with self.async_session() as session:
            # Execute all queries
            time_result = await session.execute(time_query)
            model_result = await session.execute(model_query)
            provider_result = await session.execute(provider_query)
            status_result = await session.execute(status_query)

            # Process results
            time_data = [
                {
                    "time_period": row.time_period.isoformat(),
                    "count": row.count,
                    "total_tokens": row.total_tokens,
                    "avg_duration_ms": row.avg_duration_ms,
                }
                for row in time_result.fetchall()
            ]

            model_data = [
                {
                    "model": row.model,
                    "count": row.count,
                    "total_tokens": row.total_tokens,
                    "avg_duration_ms": row.avg_duration_ms,
                }
                for row in model_result.fetchall()
            ]

            provider_data = [
                {
                    "provider": row.provider,
                    "count": row.count,
                    "total_tokens": row.total_tokens,
                    "avg_duration_ms": row.avg_duration_ms,
                }
                for row in provider_result.fetchall()
            ]

            status_data = [{"status": row.status, "count": row.count} for row in status_result.fetchall()]

            return {
                "time_interval": time_interval,
                "time_data": time_data,
                "model_data": model_data,
                "provider_data": provider_data,
                "status_data": status_data,
            }

    async def delete_trace(self, trace_id: str) -> bool:
        """Delete an LLM trace by its ID."""
        query = delete(llm_traces).where(llm_traces.c.id == trace_id)

        async with self.async_session() as session:
            async with session.begin():
                result = await session.execute(query)
                await session.commit()

                return result.rowcount > 0

    async def delete_old_traces(self, days: int = 30) -> int:
        """Delete traces older than the specified number of days."""
        # Remove timezone info for database compatibility
        cutoff_date = datetime.now(UTC).replace(tzinfo=None) - timedelta(days=days)
        query = delete(llm_traces).where(llm_traces.c.created_at < cutoff_date)

        async with self.async_session() as session:
            async with session.begin():
                result = await session.execute(query)
                await session.commit()

                return result.rowcount
