import os
import uuid
from typing import Any, Dict, List, Optional, TypedDict, cast

from dotenv import load_dotenv
from langchain.embeddings.base import Embeddings
from langchain.schema import Document
from langchain_openai import OpenAIEmbeddings
from langchain_postgres import PGVector

load_dotenv()

# Define our own Where type
Where = Dict[str, Any]


class WhereFilter(TypedDict, total=False):
    and_clause: List[Dict[str, Any]]
    or_clause: List[Dict[str, Any]]


class PostgresVectorStoreService:
    """Service to handle document embeddings and vector store operations using PostgreSQL."""

    def __init__(self, embedding_model: Optional[Embeddings] = None):
        # Get database connection details from environment
        self.connection_string = os.getenv("DATABASE_URL", "postgresql://mjh:postgres@localhost:5432/vectordb")

        # Use a prepared connection format for Heroku Postgres
        if self.connection_string.startswith("postgres://"):
            self.connection_string = self.connection_string.replace("postgres://", "postgresql://", 1)

        # Configure collection name and embedding dimension
        self.collection_name = os.getenv("PGVECTOR_COLLECTION", "document_embeddings")
        self.embedding_dimension = 1536  # OpenAI dimension, adjust as needed

        # Initialize embedding model
        self.embedding_model = embedding_model or OpenAIEmbeddings()

        # Initialize PGVector with current API parameters
        self.vector_store = PGVector(
            embeddings=self.embedding_model,
            collection_name=self.collection_name,
            connection=self.connection_string,
            pre_delete_collection=False,
        )

    def add_documents(
        self,
        documents: List[Document],
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Add documents to the vector store and return document ID."""
        if not documents:
            raise ValueError("No documents provided for embedding")

        # Generate a unique ID for this document set
        document_id = str(uuid.uuid4())

        # Prepare metadata for each document
        for doc in documents:
            doc.metadata = {
                **(doc.metadata or {}),
                "document_id": document_id,
                "user_id": user_id,
                **(metadata or {}),
            }

        # Add to vector store
        self.vector_store.add_documents(documents)

        return document_id

    def search_documents(self, query: str, user_id: str, k: int = 4) -> List[Document]:
        """Search for relevant documents based on the query and filtered by user_id."""
        results = self.vector_store.similarity_search(query, k=k, filter={"user_id": user_id})
        # Ensure we return a List[Document] to satisfy mypy
        return cast(List[Document], results)

    def delete_document(self, document_id: str, user_id: str) -> None:
        """Delete a document by its ID, only if it belongs to the specified user."""
        filter_dict = {"document_id": document_id, "user_id": user_id}

        # PGVector uses filter not where
        self.vector_store.delete(filter=filter_dict)

    def get_document_count(self, user_id: Optional[str] = None) -> int:
        """
        Get the total number of documents in the vector store.

        If user_id is provided, returns the count for that specific user.
        """
        filter_dict = {"user_id": user_id} if user_id else None

        # Get count by using the get method with the appropriate filter
        try:
            # Try to access the collection in a version-compatible way
            collection = getattr(self.vector_store, "_collection", None)  # type: ignore
            if collection is None:
                # For newer versions that use get_collection
                try:
                    # Different versions of PGVector have different APIs, ignore type errors
                    collection = self.vector_store.get_collection()  # type: ignore
                except (AttributeError, NotImplementedError, TypeError):
                    # If we can't get the collection, fall back to search
                    collection = None

            if collection is not None:
                if filter_dict:
                    docs = collection.list_documents(filter=filter_dict)  # type: ignore
                    return len(list(docs)) if docs else 0
                else:
                    # If no filter, get all documents and count them
                    docs = collection.list_documents()  # type: ignore
                    return len(list(docs)) if docs else 0

            # Fallback if collection access doesn't work
            results = self.vector_store.similarity_search("", k=9999, filter=filter_dict)
            # Make sure to return an int, not Any
            return len(results)

        except (AttributeError, NotImplementedError, TypeError):
            # Final fallback if everything else fails
            results = self.vector_store.similarity_search("", k=9999, filter=filter_dict)
            return len(results)

    def clear(self, user_id: Optional[str] = None) -> None:
        """
        Clear documents from the vector store.

        If user_id is provided, only clears documents for that specific user.
        """
        if user_id:
            filter_dict = {"user_id": user_id}
            self.vector_store.delete(filter=filter_dict)
        else:
            # Delete all documents by recreating the collection
            try:
                # Try to access the collection in a version-compatible way
                collection = getattr(self.vector_store, "_collection", None)  # type: ignore
                if collection is not None:
                    collection.delete_collection()  # type: ignore
                else:
                    # For newer versions, try to access collection methods differently
                    try:
                        # Different versions of PGVector have different APIs, ignore type errors
                        collection = self.vector_store.get_collection()  # type: ignore
                        collection.delete_collection()  # type: ignore
                    except (AttributeError, NotImplementedError, TypeError):
                        # If we can't use collection directly, try to delete all documents
                        self.vector_store.delete()
            except Exception:
                # Ignore exceptions during collection deletion
                pass

            # Recreate the vector store
            self.vector_store = PGVector(
                embeddings=self.embedding_model,
                collection_name=self.collection_name,
                connection=self.connection_string,
                pre_delete_collection=False,
            )
