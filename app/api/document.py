import base64
import logging
from typing import Optional, Union

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    Query,
    UploadFile,
    Request,
)
from fastapi.concurrency import run_in_threadpool

from app.schemas.document_schema import (
    DocumentResponse,
    DocumentSource,
    DocumentCountResponse,
    DeleteDocumentResponse,
    DeleteDocumentRequest,
    DocumentUploadRequest,
)
from app.services.document_processor import DocumentProcessor
from app.services.llm_config_service import LLMConfigService, get_llm_config_service
from app.services.vector_store import VectorStoreService
from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.vector_store_factory import create_vector_store
from app.utils.dependencies import get_llm_chain_factory
from app.services.llm_chain_factory import LLMChainFactory

# Type for vector stores - can be either service implementation
VectorStore = Union[VectorStoreService, PostgresVectorStoreService]

router = APIRouter()
logger = logging.getLogger(__name__)


def get_document_processor(
    config_service: LLMConfigService = Depends(get_llm_config_service),
    chain_factory: LLMChainFactory = Depends(get_llm_chain_factory),
):
    """Get the document processor service with chain factory for tracing"""
    return DocumentProcessor(config_service, chain_factory=chain_factory)


def get_vector_store() -> Union[VectorStoreService, PostgresVectorStoreService]:
    """Get the vector store service based on configuration"""
    return create_vector_store()


@router.post(
    "/documents",
    response_model=DocumentResponse,
    openapi_extra={
        "requestBody": {
            "content": {
                "application/json": {"schema": DocumentUploadRequest.model_json_schema()},
                "multipart/form-data": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "file": {
                                "type": "string",
                                "format": "binary",
                                "description": "PDF, DOCX, TXT, or image file to process",
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID to associate with this document",
                            },
                            "llm_provider": {
                                "type": "string",
                                "description": "LLM provider to use (openai, anthropic, google, grok)",
                            },
                            "llm_model": {
                                "type": "string",
                                "description": "Specific LLM model to use (e.g., gemini-2.0-flash)",
                            },
                        },
                        "required": ["user_id"],
                    }
                },
            }
        }
    },
)
async def upload_document(
    request: Request,
    user_id: Optional[str] = Form(None, description="User ID to associate with this document"),
    file: Optional[UploadFile] = File(None, description="PDF, DOCX, TXT, or image file to process"),
    llm_provider: Optional[str] = Form(None, description="LLM provider to use"),
    llm_model: Optional[str] = Form(None, description="Specific LLM model to use"),
    document_processor: DocumentProcessor = Depends(get_document_processor),
    vector_store: VectorStore = Depends(get_vector_store),
) -> DocumentResponse:
    """
    Upload a document for processing and embedding.

    This endpoint accepts either JSON or form data:
    - JSON: Use application/json content-type with text or base64 encoded file content
    - Form: Use multipart/form-data with a file upload field

    The document is processed, split into chunks, and stored in the vector database
    for future retrieval, associated with the specified user_id.

    **Important Update**: The system now processes ALL pages of PDFs (up to 50 pages) instead of just
    the first page. This ensures complete extraction of lawsuit data from multi-page documents.

    Default model (gemini-2.0-flash) provides excellent performance and accuracy for most documents.
    You can optionally specify a different LLM provider and model if needed.

    Available LLM providers:
    - google: Google Gemini models (default)
    - openai: OpenAI models (if supported)

    Recommended models:
    - gemini-2.0-flash: Excellent balance of speed and accuracy (default)
    - gemini-2.5-pro-exp-03-25: Alternative for specific use cases
    """
    content_type = request.headers.get("Content-Type", "")

    # Log the document upload request
    logger.info(
        f"Processing document upload request",
        extra={
            "content_type": content_type,
            "form_user_id": user_id,
            "has_file": file is not None,
            "llm_provider": llm_provider,
            "llm_model": llm_model,
        },
    )

    # Handle JSON request
    if "application/json" in content_type:
        try:
            json_request = await request.json()
        except Exception:
            logger.error("Invalid JSON format in document upload request")
            raise HTTPException(status_code=400, detail="Invalid JSON format")

        json_user_id = json_request.get("user_id")
        text = json_request.get("text")
        file_content_b64 = json_request.get("file_content")
        filename = json_request.get("filename")
        json_llm_provider = json_request.get("llm_provider")
        json_llm_model = json_request.get("llm_model")

        # Additional logging for JSON request
        logger.info(
            f"Processing JSON document upload for user_id={json_user_id}",
            extra={
                "user_id": json_user_id,
                "has_text": text is not None,
                "has_file_content": file_content_b64 is not None,
                "filename": filename,
                "llm_provider": json_llm_provider,
                "llm_model": json_llm_model,
            },
        )

        # Validate required fields
        if not json_user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Ensure user_id is a string
        if not isinstance(json_user_id, str):
            raise HTTPException(status_code=422, detail="User ID must be a string")

        if not text and not file_content_b64:
            raise HTTPException(status_code=400, detail="Either text or file_content must be provided")

        # Create model configuration
        model_config = None
        if json_llm_provider or json_llm_model:
            model_config = {"provider": json_llm_provider, "model": json_llm_model}

        # Process the request based on content type
        if file_content_b64:
            if not filename:
                raise HTTPException(
                    status_code=400,
                    detail="filename is required when uploading file_content",
                )

            try:
                # Decode base64 file content
                file_content = base64.b64decode(file_content_b64)
            except Exception:
                raise HTTPException(status_code=400, detail="Invalid base64 encoded file content")

            # Log file processing
            logger.info(
                f"Processing file for user_id={json_user_id}",
                extra={
                    "user_id": json_user_id,
                    "filename": filename,
                    "file_size": len(file_content) if file_content else 0,
                },
            )

            # Process the file with tracing enabled
            (
                documents,
                file_metadata,
            ) = await document_processor.process_file_with_tracing(
                file_content,
                filename,
                user_id=json_user_id,
                model_config=model_config,
            )

            # Create metadata
            metadata = {
                "filename": filename,
                "source": DocumentSource.FILE,
                **file_metadata,
            }
        else:
            # Process text data with tracing
            metadata = {"source": DocumentSource.TEXT}

            # Log text processing
            logger.info(
                f"Processing text for user_id={json_user_id}",
                extra={
                    "user_id": json_user_id,
                    "text_length": len(text) if text else 0,
                },
            )

            documents = await document_processor.process_text_with_tracing(text, metadata, user_id=json_user_id)

        # Add to vector store with user_id - also needs to be non-blocking
        document_id = await run_in_threadpool(vector_store.add_documents, documents, json_user_id, metadata)

        # Log successful processing
        logger.info(
            f"Successfully processed document for user_id={json_user_id}",
            extra={
                "user_id": json_user_id,
                "document_id": document_id,
                "chunks_stored": len(documents),
                "source": metadata.get("source"),
            },
        )

        return DocumentResponse(document_id=document_id, chunks_stored=len(documents), user_id=json_user_id)

    # Handle form data request (file upload)
    elif file:
        # Get user_id from query params or form data
        form = await request.form()
        # Handle form_user_id with proper typing
        raw_form_user_id = form.get("user_id", "")
        form_user_id: Optional[str] = str(raw_form_user_id) if raw_form_user_id else None

        if not user_id and not form_user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Use form_user_id if available, otherwise use query param user_id
        # Ensure we're using strings for both comparisons
        effective_user_id: str = form_user_id if form_user_id else (user_id or "")

        # Ensure user_id is a string
        if not isinstance(effective_user_id, str):
            raise HTTPException(status_code=422, detail="User ID must be a string")

        # Create model configuration
        model_config = None
        if llm_provider or llm_model:
            model_config = {"provider": llm_provider, "model": llm_model}

        # Process file upload
        file_content = await file.read()

        # Get filename safely - ensure it's a string
        raw_filename = file.filename
        upload_filename: str = str(raw_filename) if raw_filename else "unnamed_file.txt"

        # Process the file with tracing enabled
        documents, file_metadata = await document_processor.process_file_with_tracing(
            file_content,
            upload_filename,
            user_id=effective_user_id,
            model_config=model_config,
        )

        # Create metadata
        metadata = {
            "filename": upload_filename,
            "source": DocumentSource.FILE,
            **file_metadata,
        }

        # Add to vector store with user_id - also needs to be non-blocking
        document_id = await run_in_threadpool(vector_store.add_documents, documents, effective_user_id, metadata)

        return DocumentResponse(
            document_id=document_id,
            chunks_stored=len(documents),
            user_id=effective_user_id,
        )
    else:
        raise HTTPException(
            status_code=400,
            detail="Content-Type must be either application/json or multipart/form-data",
        )


@router.delete("/documents", response_model=DeleteDocumentResponse)
async def delete_document(
    request: DeleteDocumentRequest,
    vector_store: VectorStore = Depends(get_vector_store),
) -> DeleteDocumentResponse:
    """
    Delete a document from the system.

    This will remove the document and all its chunks from the vector store.
    The operation cannot be undone.
    """
    try:
        # Validate user_id
        if not request.user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Log the delete request
        logger.info(
            f"Deleting document_id={request.document_id} for user_id={request.user_id}",
            extra={
                "user_id": request.user_id,
                "document_id": request.document_id,
            },
        )

        # Delete from vector store
        success = await run_in_threadpool(vector_store.delete_document, request.document_id, request.user_id)

        # Ensure success is a boolean, not None
        success_bool = bool(success)

        if not success_bool:
            logger.warning(
                f"Document not found or already deleted: document_id={request.document_id}",
                extra={
                    "user_id": request.user_id,
                    "document_id": request.document_id,
                },
            )

        # Log the outcome
        logger.info(
            f"Document deletion {'successful' if success_bool else 'failed'}: document_id={request.document_id}",
            extra={
                "user_id": request.user_id,
                "document_id": request.document_id,
                "success": success_bool,
            },
        )

        return DeleteDocumentResponse(
            success=success_bool,
            document_id=request.document_id,
            user_id=request.user_id,
        )
    except Exception as e:
        # Log the error
        logger.error(
            f"Error deleting document: {str(e)}",
            extra={
                "user_id": request.user_id if hasattr(request, "user_id") else "unknown",
                "document_id": request.document_id if hasattr(request, "document_id") else "unknown",
            },
            exc_info=True,
        )
        # This exception is caught by FastAPI and converted to a response
        # Adding an explicit raise to satisfy mypy that all code paths return a value
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")
        # Never reached but helps mypy understand that function always returns
        return DeleteDocumentResponse(success=False, document_id=request.document_id, user_id=request.user_id)


@router.get("/documents/count", response_model=DocumentCountResponse)
async def get_document_count(
    user_id: str = Query(..., description="User ID to get document count for"),
    vector_store: VectorStore = Depends(get_vector_store),
) -> DocumentCountResponse:
    """
    Get the number of documents stored for a specific user.

    This counts unique documents, not individual chunks.
    """
    try:
        # Validate user_id
        if not user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Log the count request
        logger.info(f"Getting document count for user_id={user_id}", extra={"user_id": user_id})

        # Get count from vector store
        count = await run_in_threadpool(vector_store.get_document_count, user_id)

        # Log the result
        logger.info(
            f"Document count for user_id={user_id}: {count}",
            extra={"user_id": user_id, "count": count},
        )

        return DocumentCountResponse(count=count, user_id=user_id)
    except Exception as e:
        # Log the error
        logger.error(
            f"Error getting document count: {str(e)}",
            extra={"user_id": user_id if "user_id" in locals() else "unknown"},
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"Error getting document count: {str(e)}")
