from typing import Union, List
import logging

from fastapi import APIRouter, Depends, HTTPException

from app.schemas.query_schema import QueryRequest, QueryResponse
from app.services.llm_config_service import LLMConfigService
from app.services.llm_service import LLMService
from app.services.vector_store import VectorStoreService
from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.vector_store_factory import create_vector_store
from app.schemas.llm_config_schema import PROVIDER_MODELS, LLMProvider, ModelName
from app.services.llm_chain_factory import LLMChainFactory
from app.utils.dependencies import get_llm_chain_factory

# Type for vector stores - can be either service implementation
VectorStore = Union[VectorStoreService, PostgresVectorStoreService]

router = APIRouter()
logger = logging.getLogger(__name__)


def get_vector_store() -> VectorStore:
    """Get the vector store service based on configuration"""
    return create_vector_store()


async def get_llm_service(
    chain_factory: LLMChainFactory = Depends(get_llm_chain_factory),
) -> LLMService:
    """
    Get the LLM service for handling user queries.

    Args:
        chain_factory: The chain factory from dependency injection

    Returns:
        An initialized LLMService
    """
    # Create vector store
    vector_store = create_vector_store()

    # Create config service
    config_service = LLMConfigService()

    # Create and return the LLM service with tracing
    return LLMService(vector_store, config_service, chain_factory=chain_factory)


@router.post("/query", response_model=QueryResponse)
async def query_documents(
    request: QueryRequest,
    llm_service: LLMService = Depends(get_llm_service),
):
    """
    Query the system with a natural language question.

    The system will search for relevant documents belonging to the specified user
    and generate an answer using Retrieval Augmented Generation (RAG).

    Available LLM providers:
    - openai: OpenAI models
    - anthropic: Anthropic Claude models
    - google: Google Gemini models
    - grok: Grok AI models

    Available models by provider:

    OpenAI models:
    - gpt-4o: GPT-4o
    - gpt-4o-mini: GPT-4o Mini
    - gpt-3.5-turbo-flash: GPT-3.5 Turbo Flash
    - gpt-4.1: GPT-4.1
    - gpt-4.1-mini: GPT-4.1 Mini
    - gpt-4.1-nano: GPT-4.1 Nano
    - gpt-4.5-preview: GPT-4.5 Preview
    - gpt-4o-realtime-preview: GPT-4o Realtime Preview
    - o1: OpenAI o1
    - o1-pro: OpenAI o1 Pro
    - o3: OpenAI o3
    - o4-mini: OpenAI o4 Mini
    - o3-mini: OpenAI o3 Mini
    - o1-mini: OpenAI o1 Mini

    Anthropic models:
    - claude-3-7-sonnet-latest: Claude 3.7 Sonnet
    - claude-3-5-haiku-latest: Claude 3.5 Haiku
    - claude-3-5-sonnet-latest: Claude 3.5 Sonnet
    - claude-3-opus-latest: Claude 3 Opus

    Google models:
    - gemini-2.5-flash-preview-04-17: Gemini 2.5 Flash Preview
    - gemini-2.5-pro-exp-03-25: Gemini 2.5 Pro Experimental
    - gemini-2.0-flash: Gemini 2.0 Flash
    - gemini-2.0-flash-lite: Gemini 2.0 Flash Lite

    Grok models:
    - grok-3-beta: Grok 3 Beta
    - grok-3: Grok 3
    - grok-3-latest: Grok 3 Latest
    - grok-3-fast-beta: Grok 3 Fast Beta
    - grok-3-fast: Grok 3 Fast
    - grok-3-fast-latest: Grok 3 Fast Latest
    - grok-3-mini-beta: Grok 3 Mini Beta
    - grok-3-mini: Grok 3 Mini
    - grok-3-mini-latest: Grok 3 Mini Latest
    - grok-3-mini-fast-beta: Grok 3 Mini Fast Beta
    - grok-3-mini-fast: Grok 3 Mini Fast
    - grok-3-mini-fast-latest: Grok 3 Mini Fast Latest
    - grok-2-vision-1212: Grok 2 Vision 1212
    - grok-2-vision: Grok 2 Vision
    - grok-2-vision-latest: Grok 2 Vision Latest
    - grok-2-image-1212: Grok 2 Image 1212
    - grok-2-image: Grok 2 Image
    - grok-2-image-latest: Grok 2 Image Latest
    """
    try:
        # Get user_id from request body
        user_id = request.user_id

        # Validate user_id
        if not user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Log the query request
        logger.info(
            f"Processing query for user_id={user_id}",
            extra={
                "user_id": user_id,
                "query_length": len(request.query),
                "llm_provider": request.llm_provider,
                "llm_model": request.llm_model,
            },
        )

        # Validate that the model belongs to the selected provider
        if request.llm_provider and request.llm_model:
            # Use an empty list as default if provider is not found
            empty_list: List[ModelName] = []
            # Try to convert the string to a LLMProvider enum if possible
            try:
                provider = LLMProvider(request.llm_provider)
                provider_models = PROVIDER_MODELS.get(provider, empty_list)
            except ValueError:
                # If provider string doesn't match any enum value, use empty list
                provider_models = empty_list

            if request.llm_model not in provider_models:
                raise HTTPException(
                    status_code=400,
                    detail=f"Model {request.llm_model} is not available for provider {request.llm_provider}",
                )

        # Call the async answer_question method directly, no run_in_threadpool needed
        answer, source_ids = await llm_service.answer_question(
            question=request.query,
            user_id=user_id,
            requested_provider=request.llm_provider,
            requested_model=request.llm_model,
        )

        # Log the successful query response
        logger.info(
            f"Successfully answered query for user_id={user_id}",
            extra={
                "user_id": user_id,
                "answer_length": len(answer),
                "source_count": len(source_ids),
            },
        )

        response = QueryResponse(answer=answer, sources=source_ids)
        return response

    except Exception as e:
        # Log the error
        logger.error(
            f"Error processing query for user_id={user_id if 'user_id' in locals() else 'unknown'}: {str(e)}",
            extra={"user_id": user_id if "user_id" in locals() else "unknown", "query": request.query[:100]},
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")
