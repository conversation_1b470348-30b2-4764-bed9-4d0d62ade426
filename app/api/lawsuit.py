from typing import Union
import logging

from fastapi import APIRouter, Depends, HTTPException

from app.schemas.lawsuit_schema import (
    LawsuitData,
    LawsuitRequest,
)
from app.services.llm_config_service import LLMConfigService
from app.services.llm_service import LLMService
from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.vector_store import VectorStoreService
from app.services.vector_store_factory import create_vector_store
from app.services.llm_chain_factory import LLMChainFactory
from app.utils.dependencies import get_llm_chain_factory

# Type for vector stores - can be either service implementation
VectorStore = Union[VectorStoreService, PostgresVectorStoreService]

router = APIRouter()
logger = logging.getLogger(__name__)


async def get_llm_service(
    chain_factory: LLMChainFactory = Depends(get_llm_chain_factory),
) -> LLMService:
    """
    Get the LLM service for handling lawsuit data extraction.

    Args:
        chain_factory: The chain factory from dependency injection

    Returns:
        An initialized LLMService
    """
    # Create vector store
    vector_store = create_vector_store()

    # Create config service
    config_service = LLMConfigService()

    # Create and return the LLM service with tracing
    return LLMService(vector_store, config_service, chain_factory=chain_factory)


@router.post("/extract-lawsuit-data", response_model=LawsuitData)
async def extract_lawsuit_data(
    request: LawsuitRequest,
    llm_service: LLMService = Depends(get_llm_service),
):
    """
    Extract structured lawsuit data from documents belonging to the specified user.

    The system will analyze the user's documents and extract a structured JSON object
    containing lawsuit-related information according to the provided schema.

    Available LLM providers:
    - openai: OpenAI models
    - anthropic: Anthropic Claude models
    - google: Google Gemini models
    - grok: Grok AI models

    Available models by provider:

    OpenAI models:
    - gpt-4o: GPT-4o
    - gpt-4o-mini: GPT-4o Mini
    - gpt-3.5-turbo-flash: GPT-3.5 Turbo Flash
    - gpt-4.1: GPT-4.1
    - gpt-4.1-mini: GPT-4.1 Mini
    - gpt-4.1-nano: GPT-4.1 Nano
    - gpt-4.5-preview: GPT-4.5 Preview
    - gpt-4o-realtime-preview: GPT-4o Realtime Preview
    - o1: OpenAI o1
    - o1-pro: OpenAI o1 Pro
    - o3: OpenAI o3
    - o4-mini: OpenAI o4 Mini
    - o3-mini: OpenAI o3 Mini
    - o1-mini: OpenAI o1 Mini

    Anthropic models:
    - claude-3-7-sonnet-latest: Claude 3.7 Sonnet
    - claude-3-5-haiku-latest: Claude 3.5 Haiku
    - claude-3-5-sonnet-latest: Claude 3.5 Sonnet
    - claude-3-opus-latest: Claude 3 Opus

    Google models:
    - gemini-2.5-flash-preview-04-17: Gemini 2.5 Flash Preview
    - gemini-2.5-pro-exp-03-25: Gemini 2.5 Pro Experimental
    - gemini-2.0-flash: Gemini 2.0 Flash
    - gemini-2.0-flash-lite: Gemini 2.0 Flash Lite

    Grok models:
    - grok-3-beta: Grok 3 Beta
    - grok-3: Grok 3
    - grok-3-latest: Grok 3 Latest
    - grok-3-fast-beta: Grok 3 Fast Beta
    - grok-3-fast: Grok 3 Fast
    - grok-3-fast-latest: Grok 3 Fast Latest
    - grok-3-mini-beta: Grok 3 Mini Beta
    - grok-3-mini: Grok 3 Mini
    - grok-3-mini-latest: Grok 3 Mini Latest
    - grok-3-mini-fast-beta: Grok 3 Mini Fast Beta
    - grok-3-mini-fast: Grok 3 Mini Fast
    - grok-3-mini-fast-latest: Grok 3 Mini Fast Latest
    - grok-2-vision-1212: Grok 2 Vision 1212
    - grok-2-vision: Grok 2 Vision
    - grok-2-vision-latest: Grok 2 Vision Latest
    - grok-2-image-1212: Grok 2 Image 1212
    - grok-2-image: Grok 2 Image
    - grok-2-image-latest: Grok 2 Image Latest
    """
    try:
        # Get user_id from request body
        user_id = request.user_id

        # Validate user_id
        if not user_id:
            raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

        # Add provider/model to config from request body
        config = request.config or {}
        if request.llm_provider:
            config["provider"] = request.llm_provider
        if request.llm_model:
            config["model"] = request.llm_model

        # Log the extraction attempt with user ID and model info
        logger.info(
            f"Extracting lawsuit data for user_id={user_id}",
            extra={
                "user_id": user_id,
                "llm_provider": request.llm_provider,
                "llm_model": request.llm_model,
            },
        )

        # Extract lawsuit data using the async LLM service
        lawsuit_data = await llm_service.extract_lawsuit_data(user_id, config)

        # Log successful extraction
        logger.info(
            f"Successfully extracted lawsuit data for user_id={user_id}",
            extra={
                "user_id": user_id,
                "is_prelit": lawsuit_data.is_prelit,
                "lawsuit_exists": lawsuit_data.lawsuit_exists,
                "lawsuit_state": (lawsuit_data.lawsuit_state.value if lawsuit_data.lawsuit_state else None),
            },
        )

        return lawsuit_data

    except Exception as e:
        # Log the error
        logger.error(
            f"Error extracting lawsuit data for user_id={user_id if 'user_id' in locals() else 'unknown'}: {str(e)}",
            extra={"user_id": user_id if "user_id" in locals() else "unknown"},
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"Error extracting lawsuit data: {str(e)}")
