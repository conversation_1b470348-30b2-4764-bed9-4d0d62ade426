-r requirements.txt
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-asyncio>=0.21.1
pytest-xdist>=3.3.1
httpx>=0.27.0
pytest-mock>=3.12.0
coverage>=7.3.2
hypothesis>=6.91.0  # Property-based testing
faker>=20.1.0  # For generating fake data
pytest-timeout>=2.2.0  # For timeout tests
pytest-benchmark>=4.0.0  # For benchmarking
black>=25.1.0  # For code formatting
mypy>=1.7.1  # For type checking
types-requests>=2.31.0.0
types-PyYAML>=6.0.0
mypy-extensions>=1.0.0
types-jwt>=0.1.3  # Type stubs for JWT
types-python-jose>=3.3.0  # Type stubs for python-jose
types-Pillow>=10.2.0  # Type stubs for Pillow
pydantic-settings>=2.1.0  # For pydantic settings 