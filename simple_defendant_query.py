#!/usr/bin/env python
import requests

# API endpoint for querying
API_URL = "http://localhost:8000/api/query"

# User ID to query for
USER_ID = "gemini_test_user"


def simple_query():
    """Send a simple query asking who the defendant is."""

    # Prepare the request payload
    payload = {
        "user_id": USER_ID,
        "query": "Who is the defendant?",
        "model": "gpt-4o-mini",  # Explicitly set the model to use
    }

    try:
        # Make the POST request
        response = requests.post(API_URL, json=payload)

        # Check response
        if response.status_code == 200:
            result = response.json()

            print("\nQuery: Who is the defendant?")
            print("-" * 60)
            print(f"Answer: {result.get('answer', 'Not available')}")

            # Print sources if available
            if "sources" in result and result["sources"]:
                print("\nSources:")
                for source in result["sources"]:
                    print(f"- {source}")

            return result
        else:
            print(f"Error: Status code {response.status_code}")
            print("Response:", response.text)
            return None

    except Exception as e:
        print(f"Error during query: {str(e)}")
        return None


if __name__ == "__main__":
    simple_query()
