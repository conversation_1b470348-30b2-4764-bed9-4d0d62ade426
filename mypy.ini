[mypy]
python_version = 3.11
warn_return_any = False
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
disallow_untyped_decorators = False
no_implicit_optional = False
strict_optional = False
ignore_missing_imports = True
# Disable error about functions not returning a value
disable_error_code = func-returns-value

# Per-module options:
[mypy.plugins.pydantic.*]
ignore_missing_imports = True
follow_imports = skip

[mypy.fastapi]
ignore_missing_imports = True

[mypy.starlette]
ignore_missing_imports = True

# Ignore missing imports for third-party libraries
[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-langchain.*]
ignore_missing_imports = True

[mypy-langchain_community.*]
ignore_missing_imports = True

[mypy-langchain_openai.*]
ignore_missing_imports = True

[mypy-langchain_anthropic.*]
ignore_missing_imports = True

[mypy-langchain_google_genai.*]
ignore_missing_imports = True

[mypy-langchain_core.*]
ignore_missing_imports = True

[mypy-dotenv.*]
ignore_missing_imports = True

[mypy-google.*]
ignore_missing_imports = True

# JWT and Starlette-related imports
[mypy-jwt.*]
ignore_missing_imports = True

[mypy-starlette.*]
ignore_missing_imports = True

# Specifically for app.schemas.llm_config_schema
[mypy-app.schemas.llm_config_schema]
disallow_untyped_defs = False

# Disable no-any-return errors for specific files
[mypy-app.auth.jwt_auth]
disable_error_code = no-any-return

[mypy-app.services.vector_store]
disable_error_code = no-any-return

[mypy-app.services.llm_config_service]
disable_error_code = no-any-return

[mypy-app.services.document_processor]
disable_error_code = no-any-return

# Ignore llm_service errors about MailingAddress
[mypy-app.services.llm_service]
disable_error_code = call-arg

# Ignore missing imports for these libraries
[mypy.plugins.django-stubs]
django_settings_module = "mypy_django_settings"

[mypy.plugins.numpy.*]
ignore_missing_imports = True

[mypy.plugins.langchain_postgres.*]
ignore_missing_imports = True

[mypy.plugins.pgvector.*]
ignore_missing_imports = True

# Ignore errors in specific modules
[mypy.app.services.postgres_vector_store]
disallow_untyped_calls = False
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-langchain_postgres.*]
ignore_missing_imports = True

[mypy-pgvector.*]
ignore_missing_imports = True 