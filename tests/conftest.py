from __future__ import annotations

import os
import sys
from enum import Enum
from typing import Any, Dict, Generator
from unittest.mock import <PERSON>Mock, patch

import pytest
from fastapi import (
    APIRouter,
    FastAPI,
    HTTPException,
    Query,
    Request,
)
from fastapi.responses import JSONResponse
from fastapi.testclient import TestClient
from langchain.schema import Document
from starlette.testclient import (
    _AsyncBackend,
    _TestClientTransport,
    _is_asgi3,
    _WrapASGI2,
)

# Add the tests directory to the path for mocks
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Add environment variable setup for tests
os.environ["JWT_SECRET_KEY"] = "test-secret-key-for-ci"
os.environ["JWT_ALGORITHM"] = "HS256"

# Mock dependency functions
mock_document_processor = None
mock_vector_store = None
mock_llm_config_service = None


def get_document_processor():
    return mock_document_processor


def get_vector_store():
    return mock_vector_store


def get_config_service():
    return mock_llm_config_service


# Create mocks for app imports
class MockApp:
    def __init__(self):
        self.app = FastAPI()
        self.setup_routes()

    def setup_routes(self):
        """Setup the mock routes for testing"""
        router = APIRouter()

        @router.post("/api/documents")
        async def upload_document(
            request: Request,
        ):
            try:
                if "multipart/form-data" in request.headers.get("Content-Type", ""):
                    # Handle form data upload
                    form = await request.form()
                    user_id = form.get("user_id")
                    file = form.get("file")
                    # Model config variables intentionally not used in this mock
                    _ = form.get("llm_provider")
                    _ = form.get("llm_model")

                    if not user_id:
                        return JSONResponse(status_code=400, content={"detail": "user_id is required"})

                    if not file:
                        return JSONResponse(
                            status_code=400,
                            content={"detail": "file is required for form data uploads"},
                        )

                    # Get processors
                    processor = get_document_processor()
                    vector_store = get_vector_store()

                    if processor is None:
                        raise HTTPException(
                            status_code=500,
                            detail="Document processor is not initialized",
                        )

                    if vector_store is None:
                        raise HTTPException(status_code=500, detail="Vector store is not initialized")

                    # Process the file
                    file_content = b"mock_file_content"
                    filename = getattr(file, "filename", "unnamed_file.txt")
                    docs, _ = processor.process_file(
                        file_content,
                        filename,
                        (
                            {
                                "provider": form.get("llm_provider"),
                                "model": form.get("llm_model"),
                            }
                            if form.get("llm_provider") or form.get("llm_model")
                            else None
                        ),
                    )
                    doc_id = vector_store.add_documents(docs, user_id=user_id)
                    return {
                        "document_id": doc_id,
                        "chunks_stored": len(docs),
                        "user_id": user_id,
                        "status": "success",
                    }
                else:
                    # Handle JSON request
                    # Parse JSON request
                    try:
                        json_data = await request.json()
                    except Exception:
                        return JSONResponse(
                            status_code=400,
                            content={"detail": "Invalid JSON format"},
                        )

                    # Check required fields
                    if "user_id" not in json_data:
                        return JSONResponse(
                            status_code=422,
                            content={"detail": "Missing required parameter: user_id"},
                        )

                    user_id = json_data["user_id"]

                    # Get processors
                    processor = get_document_processor()
                    vector_store = get_vector_store()

                    if processor is None:
                        raise HTTPException(
                            status_code=500,
                            detail="Document processor is not initialized",
                        )

                    if vector_store is None:
                        raise HTTPException(status_code=500, detail="Vector store is not initialized")

                    # Validate content
                    if "text" not in json_data and "file_content" not in json_data:
                        return JSONResponse(
                            status_code=400,
                            content={"detail": "Either text or file_content must be provided"},
                        )

                    # Process file data
                    if "file_content" in json_data and json_data["file_content"]:
                        # Validate filename
                        if "filename" not in json_data or not json_data["filename"]:
                            return JSONResponse(
                                status_code=400,
                                content={"detail": "filename is required when uploading file_content"},
                            )

                        try:
                            # Mock base64 decoding
                            # In a real test, we'd actually decode the base64 here
                            file_content = b"mock_file_content"
                            filename = json_data["filename"]
                            doc_type = DocumentType.PDF if filename.endswith(".pdf") else DocumentType.TXT
                            docs, metadata = processor.process_file(file_content, doc_type)
                            doc_id = vector_store.add_documents(docs, user_id=user_id)
                            return {
                                "document_id": doc_id,
                                "chunks_stored": len(docs),
                                "user_id": user_id,
                                "status": "success",
                            }
                        except Exception as e:
                            if isinstance(e, HTTPException):
                                raise e
                            raise HTTPException(
                                status_code=500,
                                detail=f"Error processing file: {str(e)}",
                            )

                    # Process text data
                    elif "text" in json_data and json_data["text"]:
                        try:
                            text = json_data["text"]
                            docs = processor.process_text(text)
                            doc_id = vector_store.add_documents(docs, user_id=user_id)
                            return {
                                "document_id": doc_id,
                                "chunks_stored": len(docs),
                                "user_id": user_id,
                                "status": "success",
                            }
                        except Exception as e:
                            if isinstance(e, HTTPException):
                                raise e
                            raise HTTPException(
                                status_code=500,
                                detail=f"Error processing text: {str(e)}",
                            )
                    else:
                        return JSONResponse(
                            status_code=400,
                            content={"detail": "Either text or file_content must contain data"},
                        )

            except Exception as e:
                if isinstance(e, HTTPException):
                    raise e
                print(f"Error in document upload: {str(e)}")
                import traceback

                traceback.print_exc()
                raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

        @router.delete("/api/documents/{document_id}")
        async def delete_document(document_id: str, user_id: str = Query(...)):
            if not user_id:
                raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

            try:
                vector_store = get_vector_store()
                vector_store.delete_document(document_id, user_id)
                return {
                    "status": "success",
                    "message": f"Document {document_id} deleted",
                    "user_id": user_id,
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

        @router.get("/api/documents/count")
        async def get_document_count(user_id: str = Query(...)):
            if not user_id:
                raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

            try:
                vector_store = get_vector_store()
                count = vector_store.get_document_count(user_id)
                return {"status": "success", "count": count, "user_id": user_id}
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error getting document count: {str(e)}")

        self.app.include_router(router)


app = MockApp().app


# Add Enums for document types
class DocumentType(str, Enum):
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    IMAGE = "image"
    JSON = "json"


class DocumentSource(str, Enum):
    FILE = "file"
    TEXT = "text"


class LLMTask(str, Enum):
    DOCUMENT_EXTRACTION = "document_extraction"
    QUESTION_ANSWERING = "question_answering"
    LAWSUIT_EXTRACTION = "lawsuit_extraction"


class DocumentProcessor:
    """Mock DocumentProcessor"""

    def process_file(self, file_content, filename, user_id, metadata=None):
        """Process a file and return documents and metadata"""

    def process_text(self, text, user_id, metadata=None):
        """Process text and return documents"""

    def _process_pdf(self, file_path, metadata=None):
        """Process a PDF file"""

    def _process_docx(self, file_path, metadata=None):
        """Process a DOCX file"""

    def _process_text(self, file_path, metadata=None):
        """Process a text file"""

    def _process_image(self, file_path, metadata=None):
        """Process an image file"""

    def _process_unstructured(self, file_path, metadata=None):
        """Process a file using unstructured"""


class VectorStoreService:
    """Mock VectorStoreService"""

    def add_documents(self, documents, user_id, metadata=None):
        """Add documents to the vector store"""

    def get_document_count(self, user_id):
        """Get the number of documents for a user"""

    def delete_document(self, document_id, user_id):
        """Delete a document"""


class LLMConfigService:
    """Mock LLMConfigService"""

    def get_chunking_params(self, task=None):
        """Get chunking parameters for a task"""

    def get_default_provider_model_for_task(self, task=None):
        """Get default provider and model for a task"""


@pytest.fixture(autouse=True)
def setup_mocks():
    """Reset all global mocks before each test."""
    global mock_document_processor, mock_vector_store, mock_llm_config_service
    # Default to None so that they can be properly initialized in each test
    mock_document_processor = None
    mock_vector_store = None
    mock_llm_config_service = None

    yield


# Integration test helper fixture to override our global mocks with test-provided mocks
@pytest.fixture
def register_integration_mocks():
    """Register integration test mocks from the setup_mocks fixture.

    This fixture allows integration tests to provide their own mocks and have them
    properly registered in our global mock variables for route handling.
    """

    def _register(processor=None, vector_store=None):
        global mock_document_processor, mock_vector_store
        if processor:
            mock_document_processor = processor
        if vector_store:
            mock_vector_store = vector_store

    return _register


@pytest.fixture
def test_client() -> Generator:
    """Create a test client for the FastAPI app"""

    # Create a patched version of TestClient with our modified post method
    class PatchedTestClient(TestClient):
        def __init__(
            self,
            app,
            base_url="http://testserver",
            raise_server_exceptions=True,
            root_path="",
            backend="asyncio",
            backend_options=None,
            cookies=None,
            headers=None,
            follow_redirects=True,
        ):
            self.async_backend = _AsyncBackend(backend=backend, backend_options=backend_options or {})
            if _is_asgi3(app):
                asgi_app = app
            else:
                asgi_app = _WrapASGI2(app)
            self.app = asgi_app
            self.app_state = {}
            transport = _TestClientTransport(
                self.app,
                portal_factory=self._portal_factory,
                raise_server_exceptions=raise_server_exceptions,
                root_path=root_path,
                app_state=self.app_state,
            )
            if headers is None:
                headers = {}
            headers.setdefault("user-agent", "testclient")

            # Initialize httpx.Client without the 'app' parameter
            from httpx import Client

            Client.__init__(
                self,
                base_url=base_url,
                headers=headers,
                transport=transport,
                follow_redirects=follow_redirects,
                cookies=cookies,
            )

        def post(self, url, **kwargs):
            """Override the post method to handle multipart form data correctly."""
            if "files" in kwargs:
                # Remove Content-Type header for multipart form data
                if "headers" in kwargs:
                    kwargs["headers"].pop("Content-Type", None)
                # Add form data for user_id if in query params
                if "user_id" in url:
                    user_id = url.split("user_id=")[1].split("&")[0]
                    if "data" not in kwargs:
                        kwargs["data"] = {}
                    kwargs["data"]["user_id"] = user_id
            return super().post(url, **kwargs)

    with PatchedTestClient(app) as client:
        # Set default headers for all requests
        client.headers.update(
            {
                "Authorization": "Bearer test_token",
            }
        )

        yield client


@pytest.fixture
def create_mock_llm_config_service() -> MagicMock:
    """Return a mocked LLMConfigService."""
    global mock_llm_config_service
    mock = MagicMock(spec=LLMConfigService)
    # Set up default behavior
    mock.get_chunking_params.return_value = {"chunk_size": 1000, "chunk_overlap": 200}
    # Set up default providers and models
    from tests.mocks.llm_config_schema import LLMProvider, ModelName

    mock.get_default_provider_model_for_task.return_value = (
        LLMProvider.OPENAI,
        ModelName.GPT_4,
    )
    mock_llm_config_service = mock
    return mock


@pytest.fixture
def create_mock_vector_store() -> MagicMock:
    """Return a mocked VectorStoreService."""
    global mock_vector_store
    mock = MagicMock(spec=VectorStoreService)
    # Set up default behavior
    mock.add_documents.return_value = "doc_123456789"
    mock.get_document_count.return_value = 5
    mock.delete_document.return_value = None
    mock_vector_store = mock
    return mock


@pytest.fixture
def create_mock_document_processor(create_mock_llm_config_service) -> MagicMock:
    """Return a mocked DocumentProcessor."""
    global mock_document_processor
    mock = MagicMock(spec=DocumentProcessor)

    # Set up sample documents for testing
    sample_docs = [
        Document(page_content="Sample content page 1", metadata={"page": 1}),
        Document(page_content="Sample content page 2", metadata={"page": 2}),
    ]
    sample_metadata = {"doc_type": DocumentType.PDF, "extraction_method": "traditional"}

    # Set up default behavior
    mock.process_file.return_value = (sample_docs, sample_metadata)
    mock.process_text.return_value = sample_docs
    mock_document_processor = mock
    return mock


@pytest.fixture
def sample_pdf_file() -> bytes:
    """Return a minimal valid PDF file for testing."""
    # This is a minimal valid PDF file
    return (
        b"%PDF-1.7\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n"
        b"2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n"
        b"3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n"
        b"4 0 obj\n<< /Length 21 >>\nstream\nBT /F1 12 Tf 100 700 Td (Test PDF) Tj ET\nendstream\nendobj\n"
        b"xref\n0 5\n0000000000 65535 f\n0000000010 00000 n\n0000000060 00000 n\n"
        b"0000000120 00000 n\n0000000210 00000 n\n"
        b"trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n300\n%%EOF"
    )


@pytest.fixture
def sample_text_file() -> bytes:
    """Return sample text for testing."""
    return b"This is a sample text file for testing purposes.\nIt contains multiple lines of text."


@pytest.fixture
def sample_user_id() -> str:
    """Return a sample user ID for testing."""
    return "test_user_123"


@pytest.fixture
def sample_document_response() -> Dict[str, Any]:
    """Return a sample document response for testing."""
    return {
        "document_id": "doc_123456789",
        "chunks_stored": 2,
        "user_id": "test_user_123",
        "status": "success",
    }


@pytest.fixture
def sample_query_response() -> Dict[str, Any]:
    """Return a sample query response for testing."""
    return {
        "answer": "This is a sample answer based on the query.",
        "sources": ["doc_123456789"],
    }


@pytest.fixture
def sample_lawsuit_data() -> Dict[str, Any]:
    """Return sample structured lawsuit data for testing."""
    return {
        "lawsuit_exists": True,
        "lawsuit_state": "California",
        "lawsuit_amount": "$5,000.00",
        "civil_number": "CIV-2023-12345",
        "lawyer_email": "<EMAIL>",
        "law_firm": {"id": 1, "name": "Test Law Firm"},
        "plaintiff": {"id": 2, "name": "Test Plaintiff"},
        "customer": {
            "name": "John Doe",
            "phone_number": "************",
            "mailing_address": {
                "street": "123 Test St",
                "city": "Test City",
                "state": "California",
                "zip_code": "12345",
            },
        },
    }


@app.post("/api/v1/document", status_code=201)
async def mock_upload_document(
    request: Request,
):
    """Mock endpoint for uploading a document."""
    try:
        if "multipart/form-data" in request.headers.get("Content-Type", ""):
            # Handle form data upload
            form = await request.form()
            user_id = form.get("user_id")
            file = form.get("file")

            if not user_id:
                return JSONResponse(status_code=400, content={"detail": "user_id is required"})

            if not file:
                return JSONResponse(
                    status_code=400,
                    content={"detail": "file is required for form data uploads"},
                )

            # Get processors
            processor = get_document_processor()
            vector_store = get_vector_store()

            if processor is None:
                raise HTTPException(
                    status_code=500,
                    detail="Document processor is not initialized",
                )

            if vector_store is None:
                raise HTTPException(status_code=500, detail="Vector store is not initialized")

            # Process the file
            file_content = b"mock_file_content"
            filename = getattr(file, "filename", "unnamed_file.txt")
            docs, _ = processor.process_file(
                file_content,
                filename,
                (
                    {
                        "provider": form.get("llm_provider"),
                        "model": form.get("llm_model"),
                    }
                    if form.get("llm_provider") or form.get("llm_model")
                    else None
                ),
            )
            doc_id = vector_store.add_documents(docs, user_id=user_id)
            return {
                "document_id": doc_id,
                "chunks_stored": len(docs),
                "user_id": user_id,
                "status": "success",
            }
        else:
            # Handle JSON request
            # Parse JSON request
            try:
                json_data = await request.json()
            except Exception:
                return JSONResponse(
                    status_code=400,
                    content={"detail": "Invalid JSON format"},
                )

            # Check required fields
            if "user_id" not in json_data:
                return JSONResponse(
                    status_code=422,
                    content={"detail": "Missing required parameter: user_id"},
                )

            user_id = json_data["user_id"]

            # Get processors
            processor = get_document_processor()
            vector_store = get_vector_store()

            if processor is None:
                raise HTTPException(status_code=500, detail="Document processor is not initialized")

            if vector_store is None:
                raise HTTPException(status_code=500, detail="Vector store is not initialized")

            # Validate content
            if "text" not in json_data and "file_content" not in json_data:
                return JSONResponse(
                    status_code=400,
                    content={"detail": "Either text or file_content must be provided"},
                )

            # Process file data
            if "file_content" in json_data and json_data["file_content"]:
                # Validate filename
                if "filename" not in json_data or not json_data["filename"]:
                    return JSONResponse(
                        status_code=400,
                        content={"detail": "filename is required when uploading file_content"},
                    )

                try:
                    # Mock base64 decoding
                    # In a real test, we'd actually decode the base64 here
                    file_content = b"mock_file_content"
                    filename = json_data["filename"]
                    doc_type = DocumentType.PDF if filename.endswith(".pdf") else DocumentType.TXT
                    docs, metadata = processor.process_file(file_content, doc_type)
                    doc_id = vector_store.add_documents(docs, user_id=user_id)
                    return {
                        "document_id": doc_id,
                        "chunks_stored": len(docs),
                        "user_id": user_id,
                        "status": "success",
                    }
                except Exception as e:
                    if isinstance(e, HTTPException):
                        raise e
                    raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

            # Process text data
            elif "text" in json_data and json_data["text"]:
                try:
                    text = json_data["text"]
                    docs = processor.process_text(text)
                    doc_id = vector_store.add_documents(docs, user_id=user_id)
                    return {
                        "document_id": doc_id,
                        "chunks_stored": len(docs),
                        "user_id": user_id,
                        "status": "success",
                    }
                except Exception as e:
                    if isinstance(e, HTTPException):
                        raise e
                    raise HTTPException(status_code=500, detail=f"Error processing text: {str(e)}")
            else:
                return JSONResponse(
                    status_code=400,
                    content={"detail": "Either text or file_content must contain data"},
                )

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        print(f"Error in document upload: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")


@app.delete("/api/v1/document/{doc_id}")
async def mock_delete_document(doc_id: str, user_id: str):
    """Mock endpoint for deleting a document."""
    if not user_id:
        raise HTTPException(status_code=422, detail="Missing required parameter: user_id")

    try:
        vector_store = get_vector_store()
        vector_store.delete_document(doc_id, user_id)
        return JSONResponse(status_code=200, content={"status": "success"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")


@pytest.fixture(autouse=True, scope="session")
def mock_all_llms():
    """Globally mock all LLM classes to prevent real API calls during tests."""
    with (
        patch("langchain_google_genai.ChatGoogleGenerativeAI", MagicMock()),
        patch("langchain_openai.ChatOpenAI", MagicMock()),
        patch("langchain_anthropic.ChatAnthropic", MagicMock()),
    ):
        yield


# Patch FastAPI/JWT authentication for tests to simulate invalid token
@pytest.fixture(autouse=True)
def patch_auth_dependency(monkeypatch):
    """Patch the authentication dependency to simulate invalid tokens for auth tests."""
    from fastapi import HTTPException, status

    def fake_verify_token(token: str):
        # Always raise unauthorized for invalid tokens
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Patch the verify_token function in app.auth.jwt_auth
    monkeypatch.setattr("app.auth.jwt_auth.verify_token", fake_verify_token)
    yield
