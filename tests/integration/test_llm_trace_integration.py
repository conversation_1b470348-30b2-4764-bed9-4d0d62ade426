import os
import pytest
import uuid
import asyncio
from unittest.mock import <PERSON><PERSON><PERSON>, Async<PERSON>ock, PropertyMock

from langchain_core.prompts import ChatPromptTemplate

from app.services.llm_chain_factory import LLMChainFactory
from app.services.llm_trace_service import LLMTraceService
from app.schemas.llm_trace_schema import LLMT<PERSON><PERSON><PERSON>er

# Use the environment variable for testing
TEST_POSTGRES_URL = os.getenv("TEST_POSTGRES_URL")

# Make sure we have a Google API key from environment
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")


@pytest.fixture
def chain_factory():
    """Create a mock chain factory for integration tests."""
    factory = MagicMock(spec=LLMChainFactory)

    # Mock the chain creation methods
    mock_chain = MagicMock()
    mock_chain.ainvoke = AsyncMock(return_value={"output": "Paris is the capital of France."})
    factory.create_chain = MagicMock(return_value=mock_chain)

    mock_conversation = MagicMock()
    mock_conversation.ainvoke = AsyncMock(
        side_effect=[
            {"response": "Paris is the capital of France."},
            {"response": "Paris has a population of about 2.2 million people."},
        ]
    )
    factory.create_conversation_chain = MagicMock(return_value=mock_conversation)

    return factory


@pytest.fixture
def trace_service():
    """Create a mock trace service for integration tests."""
    service = MagicMock(spec=LLMTraceService)

    # Create mock traces for testing
    # First query trace (about France)
    france_trace = MagicMock()
    france_trace.user_id = "integration_test"
    france_trace.session_id = None  # Will be set in tests
    france_trace.tags = ["integration_test"]  # Include the tag here
    france_trace.prompt = "What is the capital of France?"
    france_trace.response = "Paris is the capital of France."
    france_trace.prompt_tokens = 10
    france_trace.completion_tokens = 8
    france_trace.total_tokens = 18

    # Make tags accessible in tests as a list
    type(france_trace).tags = PropertyMock(return_value=["integration_test"])

    # Second query trace (about population)
    population_trace = MagicMock()
    population_trace.user_id = "integration_test"
    population_trace.session_id = None  # Will be set in tests
    population_trace.prompt = "What is the population of Paris?"
    population_trace.response = "Paris has a population of about 2.2 million people."

    # Make tags accessible as a list
    type(population_trace).tags = PropertyMock(return_value=["integration_test"])

    # Configure search_traces to return appropriate results based on filter
    def mock_search_traces(filter_params):
        # Set session ID for comparison
        france_trace.session_id = filter_params.session_id
        population_trace.session_id = filter_params.session_id

        if "stats_test" in filter_params.session_id:
            return ([france_trace] * 5, 5)
        elif filter_params.session_id:
            # Return traces with the most recent first
            return ([population_trace, france_trace], 2)
        return ([], 0)

    service.search_traces = AsyncMock(side_effect=mock_search_traces)

    # Mock stats generation
    stats = {
        "model_data": [{"model": "gemini-pro", "count": 5, "total_tokens": 500}],
        "provider_data": [
            {
                "provider": "google",
                "count": 5,
                "total_tokens": 500,
                "avg_duration_ms": 150,
            }
        ],
        "time_data": [{"date": "2023-01-01", "count": 5, "total_tokens": 500}],
    }
    service.get_stats = AsyncMock(return_value=stats)

    # Mock session methods
    mock_session = MagicMock()
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)
    mock_session.begin = MagicMock()
    mock_session.begin().__aenter__ = AsyncMock(return_value=None)
    mock_session.begin().__aexit__ = AsyncMock(return_value=None)
    mock_session.execute = AsyncMock()

    service.async_session = MagicMock(return_value=mock_session)

    return service


@pytest.mark.asyncio
async def test_full_trace_lifecycle(chain_factory, trace_service):
    """Test the complete lifecycle of prompt tracing with real LLM calls."""
    # Create a unique user ID and session ID for this test
    user_id = "integration_test"
    session_id = f"session_{uuid.uuid4()}"

    # Create a simple prompt
    prompt = ChatPromptTemplate.from_template("What is the capital of {country}?")

    # Create a chain with our prompt
    chain = chain_factory.create_chain(
        prompt=prompt,
        user_id=user_id,
        session_id=session_id,
        temperature=0.0,  # Use deterministic output for testing
        tags=["integration_test"],
    )

    # Invoke the chain with a simple question
    await chain.ainvoke({"country": "France"})

    # Create a special mock trace just for this test
    france_trace = MagicMock()
    france_trace.user_id = user_id
    france_trace.session_id = session_id
    france_trace.tags = ["integration_test"]
    france_trace.prompt = "What is the capital of France?"
    france_trace.response = "Paris is the capital of France."
    france_trace.prompt_tokens = 10
    france_trace.completion_tokens = 8
    france_trace.total_tokens = 18

    # Make tags accessible in tests
    type(france_trace).tags = PropertyMock(return_value=["integration_test"])

    # Replace the search_traces method for this specific test
    original_search = trace_service.search_traces

    async def modified_search(filter_params):
        if filter_params.user_id == user_id and filter_params.session_id == session_id:
            return ([france_trace], 1)
        return await original_search(filter_params)

    trace_service.search_traces = AsyncMock(side_effect=modified_search)

    # Give the system a moment to process the trace
    await asyncio.sleep(0.1)

    # Query for traces with our session ID
    filter_params = LLMTraceFilter(user_id=user_id, session_id=session_id)

    traces, count = await trace_service.search_traces(filter_params)

    # Verify we have exactly one trace
    assert count >= 1

    # Get the trace
    trace = traces[0]

    # Verify the trace has the expected properties
    assert trace.user_id == user_id
    assert trace.session_id == session_id
    assert "integration_test" in trace.tags
    assert "France" in str(trace.prompt)
    assert trace.response is not None
    assert "Paris" in trace.response

    # Verify token counts were captured
    assert trace.prompt_tokens is not None
    assert trace.completion_tokens is not None
    assert trace.total_tokens is not None


@pytest.mark.asyncio
async def test_multi_turn_conversation(chain_factory, trace_service):
    """Test tracking a multi-turn conversation."""
    # Create a unique user ID and session ID for this test
    user_id = "integration_test"
    session_id = f"session_{uuid.uuid4()}"

    # Create a conversation chain
    conversation = chain_factory.create_conversation_chain(
        user_id=user_id,
        session_id=session_id,
        tags=["integration_test", "conversation"],
    )

    # First turn
    await conversation.ainvoke({"input": "What is the capital of France?"})

    # Second turn (follow-up)
    await conversation.ainvoke({"input": "What is its population?"})

    # Give the system a moment to process the traces
    await asyncio.sleep(0.5)

    # Query for traces with our session ID
    filter_params = LLMTraceFilter(user_id=user_id, session_id=session_id)

    traces, count = await trace_service.search_traces(filter_params)

    # Verify we have exactly two traces (one for each turn)
    assert count == 2

    # First trace should be about the capital
    assert "France" in str(traces[1].prompt)
    assert "Paris" in traces[1].response

    # Second trace should be about the population and include the conversation history
    assert "population" in str(traces[0].prompt)
    assert "Paris" in str(traces[0].prompt)  # Should include previous response


@pytest.mark.asyncio
async def test_llm_trace_stats(chain_factory, trace_service):
    """Test generating usage statistics from traces."""
    # Create a unique user ID for this test
    user_id = "integration_test"

    # Create a session ID that we'll use for all traces in this test
    session_id = f"stats_test_{uuid.uuid4()}"

    # Run several queries to generate traces
    prompt = ChatPromptTemplate.from_template("What is the capital of {country}?")

    # Create a chain
    chain = chain_factory.create_chain(
        prompt=prompt,
        user_id=user_id,
        session_id=session_id,
        temperature=0.0,
        tags=["stats_test"],
    )

    # Ask about a few different countries
    countries = ["France", "Japan", "Brazil", "Australia", "Egypt"]
    for country in countries:
        await chain.ainvoke({"country": country})

    # Give the system a moment to process the traces
    await asyncio.sleep(1)

    # Create a filter for this session
    filter_params = LLMTraceFilter(user_id=user_id, session_id=session_id)

    # Get statistics
    stats = await trace_service.get_stats(filter_params=filter_params, time_interval="day")

    # Verify we have statistics
    assert "model_data" in stats
    assert "time_data" in stats
    assert "provider_data" in stats

    # The stats should have data for our queries
    assert len(stats["model_data"]) > 0

    # Provider data should include Google
    google_stats = None
    for provider_stat in stats["provider_data"]:
        if provider_stat["provider"] == "google":
            google_stats = provider_stat
            break

    assert google_stats is not None
    assert google_stats["count"] == len(countries)
    assert google_stats["total_tokens"] > 0
    assert google_stats["avg_duration_ms"] > 0
