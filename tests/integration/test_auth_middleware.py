import jwt
import pytest
from fastapi import FastAP<PERSON>
from starlette.middleware.base import BaseHTTPMiddleware

from app.auth.middleware import JWTAuthMiddleware


class TestAuthMiddleware:
    """Integration tests for the JWT Authentication middleware."""

    @pytest.fixture
    def mock_env_vars(self, monkeypatch):
        """Set up mock environment variables for testing."""
        monkeypatch.setenv("JWT_SECRET_KEY", "test-secret-key-for-ci")
        monkeypatch.setenv("JWT_ALGORITHM", "HS256")

    @pytest.fixture
    def app_with_auth(self):
        """Create a test app with the JWT auth middleware."""
        app = FastAPI()

        # Add JWT Authentication middleware with excluded paths
        auth_exclude_paths = ["/health", "/excluded"]
        app.add_middleware(
            BaseHTTPMiddleware,
            dispatch=JWTAuthMiddleware(app, exclude_paths=auth_exclude_paths),
        )

        # Add a test protected endpoint
        @app.get("/protected")
        async def protected():
            return {"status": "success"}

        # Add an excluded endpoint
        @app.get("/excluded")
        async def excluded():
            return {"status": "public"}

        # Add a health endpoint
        @app.get("/health")
        async def health():
            return {"status": "healthy"}

        return app

    @pytest.fixture
    def client(self, app_with_auth):
        """Create a test client for the app."""
        # Import the Starlette components
        from starlette.testclient import (
            _AsyncBackend,
            _TestClientTransport,
            _is_asgi3,
            _WrapASGI2,
        )
        from fastapi.testclient import TestClient
        from httpx import Client

        # Create a patched version of TestClient that doesn't pass 'app' to httpx.Client.__init__
        class PatchedTestClient(TestClient):
            def __init__(
                self,
                app,
                base_url="http://testserver",
                raise_server_exceptions=True,
                root_path="",
                backend="asyncio",
                backend_options=None,
                cookies=None,
                headers=None,
                follow_redirects=True,
            ):
                self.async_backend = _AsyncBackend(backend=backend, backend_options=backend_options or {})
                if _is_asgi3(app):
                    asgi_app = app
                else:
                    asgi_app = _WrapASGI2(app)
                self.app = asgi_app
                self.app_state = {}
                transport = _TestClientTransport(
                    self.app,
                    portal_factory=self._portal_factory,
                    raise_server_exceptions=raise_server_exceptions,
                    root_path=root_path,
                    app_state=self.app_state,
                )
                if headers is None:
                    headers = {}
                headers.setdefault("user-agent", "testclient")

                # Initialize httpx.Client without the 'app' parameter
                Client.__init__(
                    self,
                    base_url=base_url,
                    headers=headers,
                    transport=transport,
                    follow_redirects=follow_redirects,
                    cookies=cookies,
                )

        return PatchedTestClient(app_with_auth)

    @pytest.fixture
    def valid_token(self):
        """Generate a valid JWT token for testing."""
        payload = {"app_name": "test-app"}
        return jwt.encode(payload, "test-secret-key-for-ci", algorithm="HS256")

    def test_protected_endpoint_with_valid_token(self, client, valid_token, mock_env_vars):
        """Test that a protected endpoint works with a valid token."""
        headers = {"Authorization": f"Bearer {valid_token}"}
        response = client.get("/protected", headers=headers)
        assert response.status_code == 200
        assert response.json() == {"status": "success"}

    def test_protected_endpoint_without_token(self, client, mock_env_vars):
        """Test that a protected endpoint returns 401 without a token."""
        response = client.get("/protected")
        assert response.status_code == 401
        assert "Authentication required" in response.json()["detail"]

    def test_protected_endpoint_with_invalid_token(self, client, mock_env_vars):
        """Test that a protected endpoint returns 401 with an invalid token."""
        headers = {"Authorization": "Bearer invalid-token"}
        response = client.get("/protected", headers=headers)
        assert response.status_code == 401
        assert "detail" in response.json()

    def test_excluded_endpoint_without_token(self, client, mock_env_vars):
        """Test that an excluded endpoint works without a token."""
        response = client.get("/excluded")
        assert response.status_code == 200
        assert response.json() == {"status": "public"}

    def test_health_endpoint_without_token(self, client, mock_env_vars):
        """Test that the health endpoint works without a token."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy"}
