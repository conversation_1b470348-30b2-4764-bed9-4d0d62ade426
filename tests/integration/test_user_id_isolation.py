import pytest
import uuid

from app.services.vector_store import VectorStoreService
from app.schemas.document_schema import DocumentSource


@pytest.fixture
def vector_store():
    """Create a real in-memory vector store for testing"""
    return VectorStoreService()


@pytest.mark.asyncio
async def test_vector_store_isolates_documents_by_user_id(vector_store):
    """Test that the vector store properly isolates documents by user_id"""
    # Create test users
    user_1_id = f"test_user_{uuid.uuid4().hex[:8]}"
    user_2_id = f"test_user_{uuid.uuid4().hex[:8]}"

    # Create sample documents for each user
    from langchain.schema import Document

    # Documents for user 1
    user_1_docs = [Document(page_content="This is user 1's document", metadata={"source": DocumentSource.TEXT})]

    # Documents for user 2
    user_2_docs = [Document(page_content="This is user 2's document", metadata={"source": DocumentSource.TEXT})]

    # Add documents to vector store
    vector_store.add_documents(user_1_docs, user_1_id)
    user_2_doc_id = vector_store.add_documents(user_2_docs, user_2_id)

    # Verify each user has the correct document count
    user_1_count = vector_store.get_document_count(user_1_id)
    user_2_count = vector_store.get_document_count(user_2_id)

    assert user_1_count == 1
    assert user_2_count == 1

    # Verify that search is isolated by user_id
    user_1_search = vector_store.search_documents("document", user_1_id)
    user_2_search = vector_store.search_documents("document", user_2_id)

    # Each user should only get their own documents
    assert len(user_1_search) == 1
    assert len(user_2_search) == 1
    assert "user 1's document" in user_1_search[0].page_content
    assert "user 2's document" in user_2_search[0].page_content

    # Verify that user_1 cannot delete user_2's document
    # This tests that the vector store properly checks user_id when deleting
    vector_store.delete_document(user_2_doc_id, user_1_id)

    # User 2's document should still exist
    user_2_count_after = vector_store.get_document_count(user_2_id)
    assert user_2_count_after == 1

    # Now user_2 deletes their own document
    vector_store.delete_document(user_2_doc_id, user_2_id)

    # Verify user_2's document is gone
    user_2_count_final = vector_store.get_document_count(user_2_id)
    assert user_2_count_final == 0

    # But user_1's document is still there
    user_1_count_final = vector_store.get_document_count(user_1_id)
    assert user_1_count_final == 1
