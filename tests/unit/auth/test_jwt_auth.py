import jwt
import pytest
from fastapi import HTTPException
from typing import Union, cast

from app.auth.jwt_auth import verify_token


class TestJWTAuth:
    """Tests for JWT authentication."""

    @pytest.fixture
    def mock_env_vars(self, monkeypatch):
        """Set up mock environment variables for testing."""
        # Use the same secret key as set in conftest.py
        monkeypatch.setenv("JWT_SECRET_KEY", "test-secret-key-for-ci")
        monkeypatch.setenv("JWT_ALGORITHM", "HS256")

    def test_verify_token_valid(self, mock_env_vars):
        """Test that a valid token is properly verified."""
        # Create a valid token
        payload = {"app_name": "test-app"}
        token: Union[str, bytes] = jwt.encode(payload, "test-secret-key-for-ci", algorithm="HS256")

        # Ensure token is a string
        if isinstance(token, bytes):
            token = token.decode("utf-8")

        token_str: str = cast(str, token)  # Tell mypy this is definitely a string now

        # Verify the token
        result = verify_token(token_str)

        # Check the result
        assert result == payload

    def test_verify_token_invalid_signature(self, mock_env_vars):
        """Test that a token with invalid signature is rejected."""
        # Create a token with a different secret key
        payload = {"app_name": "test-app"}
        token: Union[str, bytes] = jwt.encode(payload, "wrong-secret-key", algorithm="HS256")

        # Ensure token is a string
        if isinstance(token, bytes):
            token = token.decode("utf-8")

        token_str: str = cast(str, token)  # Tell mypy this is definitely a string now

        # Verify the token should raise an exception
        with pytest.raises(HTTPException) as excinfo:
            verify_token(token_str)

        # Check the exception
        assert excinfo.value.status_code == 401
        assert "Invalid authentication token" in excinfo.value.detail

    def test_verify_token_expired(self, mock_env_vars):
        """Test that an expired token is rejected."""
        # Create an expired token
        import time

        payload = {
            "app_name": "test-app",
            "exp": int(time.time()) - 3600,  # Expired 1 hour ago
        }
        token: Union[str, bytes] = jwt.encode(payload, "test-secret-key-for-ci", algorithm="HS256")

        # Ensure token is a string
        if isinstance(token, bytes):
            token = token.decode("utf-8")

        token_str: str = cast(str, token)  # Tell mypy this is definitely a string now

        # Verify the token should raise an exception
        with pytest.raises(HTTPException) as excinfo:
            verify_token(token_str)

        # Check the exception
        assert excinfo.value.status_code == 401
        assert "Token has expired" in excinfo.value.detail

    def test_verify_token_malformed(self, mock_env_vars):
        """Test that a malformed token is rejected."""
        # Create an invalid token
        token = "not-a-valid-jwt-token"

        # Verify the token should raise an exception
        with pytest.raises(HTTPException) as excinfo:
            verify_token(token)

        # Check the exception
        assert excinfo.value.status_code == 401
        assert "Invalid authentication token" in excinfo.value.detail
