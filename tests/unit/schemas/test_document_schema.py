from typing import List, Optional

import pytest
from pydantic import BaseModel

from tests.conftest import DocumentSource, DocumentType


class DocumentMetadata(BaseModel):
    source: DocumentSource
    user_id: str
    doc_type: Optional[DocumentType] = None
    filename: Optional[str] = None
    page_numbers: Optional[List[int]] = None
    author: Optional[str] = None
    creation_date: Optional[str] = None
    custom_metadata: Optional[dict] = None


class DocumentRequest(BaseModel):
    text: Optional[str] = None
    metadata: Optional[DocumentMetadata] = None

    def __init__(self, **data):
        if "metadata" in data and data["metadata"] is None:
            pass  # Keep None as None
        super().__init__(**data)


class DocumentResponse(BaseModel):
    document_id: str
    chunks_stored: int
    user_id: str
    status: str = "success"


class TestDocumentSchema:
    """Tests for document schema validation."""

    def test_document_type_values(self):
        """Test that DocumentType has expected values."""
        assert DocumentType.PDF == "pdf"
        assert DocumentType.DOCX == "docx"
        assert DocumentType.TXT == "txt"
        assert DocumentType.IMAGE == "image"
        assert DocumentType.JSON == "json"

    def test_document_source_values(self):
        """Test that DocumentSource has expected values."""
        assert DocumentSource.FILE == "file"
        assert DocumentSource.TEXT == "text"

    def test_document_metadata_valid(self):
        """Test creating a valid DocumentMetadata."""
        metadata = DocumentMetadata(
            source=DocumentSource.FILE,
            user_id="test_user",
            doc_type=DocumentType.PDF,
            filename="test.pdf",
            page_numbers=[1, 2, 3],
            author="Test Author",
            creation_date="2023-01-01",
            custom_metadata={"key": "value"},
        )

        assert metadata.source == DocumentSource.FILE
        assert metadata.user_id == "test_user"
        assert metadata.doc_type == DocumentType.PDF
        assert metadata.filename == "test.pdf"
        assert metadata.page_numbers == [1, 2, 3]
        assert metadata.author == "Test Author"
        assert metadata.creation_date == "2023-01-01"
        assert metadata.custom_metadata == {"key": "value"}

    def test_document_metadata_minimal(self):
        """Test creating a minimal valid DocumentMetadata."""
        metadata = DocumentMetadata(source=DocumentSource.TEXT, user_id="test_user")

        assert metadata.source == DocumentSource.TEXT
        assert metadata.user_id == "test_user"
        assert metadata.doc_type is None
        assert metadata.filename is None
        assert metadata.page_numbers is None
        assert metadata.author is None
        assert metadata.creation_date is None
        assert metadata.custom_metadata is None

    def test_document_request_valid(self):
        """Test creating a valid DocumentRequest."""
        metadata = DocumentMetadata(source=DocumentSource.TEXT, user_id="test_user")
        request = DocumentRequest(
            text="This is test text",
            metadata=metadata,
        )

        assert request.text == "This is test text"
        assert request.metadata is not None
        assert request.metadata.source == DocumentSource.TEXT
        assert request.metadata.user_id == "test_user"

    def test_document_request_minimal(self):
        """Test creating a minimal valid DocumentRequest."""
        request = DocumentRequest()

        assert request.text is None
        assert request.metadata is None

    def test_document_response_valid(self):
        """Test creating a valid DocumentResponse."""
        response = DocumentResponse(document_id="doc_123456789", chunks_stored=5, user_id="test_user")

        assert response.document_id == "doc_123456789"
        assert response.chunks_stored == 5
        assert response.user_id == "test_user"
        assert response.status == "success"  # Default value

    def test_document_response_custom_status(self):
        """Test creating a DocumentResponse with custom status."""
        response = DocumentResponse(
            document_id="doc_123456789",
            chunks_stored=5,
            user_id="test_user",
            status="pending",
        )

        assert response.status == "pending"

    def test_document_response_validation(self):
        """Test that validation works for DocumentResponse."""
        # The class should validate its fields correctly
        valid_doc = DocumentResponse(document_id="doc_123456789", chunks_stored=5, user_id="test_user")
        assert valid_doc.document_id == "doc_123456789"

        # But trying to validate with a string instead of int for chunks_stored should fail
        with pytest.raises(Exception):  # Using broader Exception to catch ValidationError in all Pydantic versions
            DocumentResponse(
                document_id="doc_123456789",
                chunks_stored="not-an-integer",  # type: ignore
                user_id="test_user",
            )
