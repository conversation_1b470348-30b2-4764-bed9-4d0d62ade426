import os
import pytest
import pytest_asyncio
from datetime import datetime, timedelta, UTC

from sqlalchemy import text
from app.schemas.llm_trace_schema import LLMTraceCreate, LLMTraceFilter, TraceStatus
from app.services.llm_trace_service import LLMTraceService

# Use the environment variable for testing
TEST_POSTGRES_URL = os.getenv("TEST_POSTGRES_URL")
if not TEST_POSTGRES_URL:
    pytest.skip("TEST_POSTGRES_URL not set", allow_module_level=True)

# Make sure we're using the asyncpg driver
if not TEST_POSTGRES_URL.startswith("postgresql+asyncpg://"):
    # Replace postgresql:// with postgresql+asyncpg://
    if TEST_POSTGRES_URL.startswith("postgresql://"):
        TEST_POSTGRES_URL = TEST_POSTGRES_URL.replace("postgresql://", "postgresql+asyncpg://")
    # Replace postgres:// with postgresql+asyncpg://
    elif TEST_POSTGRES_URL.startswith("postgres://"):
        TEST_POSTGRES_URL = TEST_POSTGRES_URL.replace("postgres://", "postgresql+asyncpg://")
    else:
        # If it doesn't start with either, prepend the proper protocol
        TEST_POSTGRES_URL = f"postgresql+asyncpg://{TEST_POSTGRES_URL}"


@pytest_asyncio.fixture
async def trace_service():
    """Create and initialize a real trace service."""
    service = LLMTraceService(postgres_url=TEST_POSTGRES_URL)
    await service.initialize()

    # Clean up any existing test data before running tests
    async with service.async_session() as session:
        async with session.begin():
            await session.execute(text("DELETE FROM llm_traces WHERE user_id = 'test_user'"))

    yield service

    # Clean up after tests
    async with service.async_session() as session:
        async with session.begin():
            await session.execute(text("DELETE FROM llm_traces WHERE user_id = 'test_user'"))


@pytest.mark.asyncio
async def test_create_trace(trace_service):
    """Test creating a trace entry."""
    # Create a simple trace
    trace = LLMTraceCreate(
        user_id="test_user",
        session_id="test_session",
        provider="google",
        model="gemini-pro",
        prompt="What is the capital of France?",
        response="The capital of France is Paris.",
        status=TraceStatus.SUCCESS,
        parameters={"temperature": 0.0},
        tags=["test", "simple_query"],
    )

    # Save the trace
    result = await trace_service.create_trace(trace)

    # Verify the result
    assert result.id is not None
    assert result.user_id == "test_user"
    assert result.session_id == "test_session"
    assert result.provider == "google"
    assert result.model == "gemini-pro"
    assert result.prompt == "What is the capital of France?"
    assert result.response == "The capital of France is Paris."
    assert result.status == TraceStatus.SUCCESS
    assert result.parameters == {"temperature": 0.0}
    assert "test" in result.tags
    assert "simple_query" in result.tags
    assert result.created_at is not None


@pytest.mark.asyncio
async def test_create_trace_with_complex_prompt(trace_service):
    """Test creating a trace with a complex prompt structure."""
    # Create a trace with a structured prompt
    complex_prompt = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"},
    ]

    trace = LLMTraceCreate(
        user_id="test_user",
        provider="google",
        model="gemini-pro",
        prompt=complex_prompt,
        response="The capital of France is Paris.",
        status=TraceStatus.SUCCESS,
    )

    # Save the trace
    result = await trace_service.create_trace(trace)

    # Retrieve the saved trace
    saved_trace = await trace_service.get_trace(result.id)

    # Verify the prompt was properly serialized and deserialized
    assert saved_trace is not None
    assert isinstance(saved_trace.prompt, list)
    assert len(saved_trace.prompt) == 2
    assert saved_trace.prompt[0]["role"] == "system"
    assert saved_trace.prompt[1]["role"] == "user"


@pytest.mark.asyncio
async def test_search_traces(trace_service):
    """Test searching for traces with filters."""
    # Create multiple traces
    for i in range(5):
        trace = LLMTraceCreate(
            user_id="test_user",
            provider="google",
            model="gemini-pro",
            prompt=f"Test query {i}",
            response=f"Test response {i}",
            status=TraceStatus.SUCCESS,
            tags=["test", f"query_{i}"],
        )
        await trace_service.create_trace(trace)

    # Create one with different provider
    diff_provider_trace = LLMTraceCreate(
        user_id="test_user",
        provider="openai",
        model="gpt-4",
        prompt="OpenAI test",
        response="OpenAI response",
        status=TraceStatus.SUCCESS,
        tags=["test", "openai"],
    )
    await trace_service.create_trace(diff_provider_trace)

    # Create one with error status
    error_trace = LLMTraceCreate(
        user_id="test_user",
        provider="google",
        model="gemini-pro",
        prompt="Error test",
        response=None,
        status=TraceStatus.ERROR,
        error_message="Test error",
        tags=["test", "error"],
    )
    await trace_service.create_trace(error_trace)

    # Search for all test_user traces
    filter_all = LLMTraceFilter(user_id="test_user")
    traces_all, count_all = await trace_service.search_traces(filter_all)
    assert count_all == 7  # 5 + 1 + 1

    # Search for Google provider only
    filter_google = LLMTraceFilter(user_id="test_user", provider="google")
    traces_google, count_google = await trace_service.search_traces(filter_google)
    assert count_google == 6  # 5 + 1

    # Search for error status
    filter_error = LLMTraceFilter(user_id="test_user", status=TraceStatus.ERROR)
    traces_error, count_error = await trace_service.search_traces(filter_error)
    assert count_error == 1
    assert traces_error[0].error_message == "Test error"

    # Search with specific tag
    filter_tag = LLMTraceFilter(user_id="test_user", tags=["openai"])
    traces_tag, count_tag = await trace_service.search_traces(filter_tag)
    assert count_tag == 1
    assert traces_tag[0].provider == "openai"


@pytest.mark.asyncio
async def test_delete_trace(trace_service):
    """Test deleting a trace."""
    # Create a trace
    trace = LLMTraceCreate(
        user_id="test_user",
        provider="google",
        model="gemini-pro",
        prompt="Delete this",
        response="This will be deleted",
        status=TraceStatus.SUCCESS,
    )
    result = await trace_service.create_trace(trace)

    # Verify it exists
    saved_trace = await trace_service.get_trace(result.id)
    assert saved_trace is not None

    # Delete it
    success = await trace_service.delete_trace(result.id)
    assert success is True

    # Verify it's gone
    deleted_trace = await trace_service.get_trace(result.id)
    assert deleted_trace is None


@pytest.mark.asyncio
async def test_delete_old_traces(trace_service):
    """Test deleting old traces."""
    # Create a trace to be treated as old
    old_trace = LLMTraceCreate(
        user_id="test_user",
        provider="google",
        model="gemini-pro",
        prompt="Old trace",
        response="This is old",
        status=TraceStatus.SUCCESS,
    )
    old_result = await trace_service.create_trace(old_trace)

    # Create a recent trace
    recent_trace = LLMTraceCreate(
        user_id="test_user",
        provider="google",
        model="gemini-pro",
        prompt="Recent trace",
        response="This is recent",
        status=TraceStatus.SUCCESS,
    )
    recent_result = await trace_service.create_trace(recent_trace)

    # Manually update old trace's created_at to 4 days ago
    # Use naive datetime without timezone info for compatibility
    four_days_ago = datetime.now(UTC).replace(tzinfo=None) - timedelta(days=4)
    async with trace_service.async_session() as session:
        async with session.begin():
            update_sql = text(f"UPDATE llm_traces SET created_at = '{four_days_ago}' WHERE id = '{old_result.id}'")
            await session.execute(update_sql)

    # Delete traces older than 3 days
    deleted_count = await trace_service.delete_old_traces(days=3)

    # Verify at least one trace was deleted
    assert deleted_count >= 1

    # Verify old trace is gone but recent trace remains
    old_check = await trace_service.get_trace(old_result.id)
    recent_check = await trace_service.get_trace(recent_result.id)

    assert old_check is None
    assert recent_check is not None
