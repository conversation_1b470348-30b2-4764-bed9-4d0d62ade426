import os
from unittest.mock import MagicMock, patch, Mock

import pytest
from langchain.schema import Document

from app.services.postgres_vector_store import PostgresVectorStoreService


@pytest.fixture
def mock_pgvector():
    """Mock the PGVector class."""
    # Create instance mock first
    pgvector_instance = Mock()

    # Create class mock that returns the instance
    mock_pgvector_class = Mock()
    mock_pgvector_class.return_value = pgvector_instance

    # Patch the class
    with patch("app.services.postgres_vector_store.PGVector", mock_pgvector_class):
        yield pgvector_instance


@pytest.fixture
def mock_embedding_model():
    """Mock an embedding model."""
    return MagicMock()


class TestPostgresVectorStoreService:
    """Tests for the PostgresVectorStoreService."""

    def test_init_with_default_values(self, mock_pgvector, mock_embedding_model):
        """Test initializing with default values."""
        # Mock environment variables
        with patch.dict(os.environ, {"DATABASE_URL": "postgresql://user:pass@localhost:5432/test"}):
            # Create the service
            service = PostgresVectorStoreService(mock_embedding_model)

            # Verify properties were set correctly
            assert service.connection_string == "postgresql://user:pass@localhost:5432/test"
            assert service.collection_name == "document_embeddings"
            assert service.embedding_model == mock_embedding_model
            assert service.vector_store == mock_pgvector

    def test_init_handles_postgres_url_format(self, mock_pgvector, mock_embedding_model):
        """Test handling of postgres:// URL format."""
        # Mock environment variables with postgres:// protocol
        with patch.dict(os.environ, {"DATABASE_URL": "postgres://user:pass@localhost:5432/test"}):
            # Create the service
            service = PostgresVectorStoreService(mock_embedding_model)

            # Verify URL was converted to postgresql://
            assert service.connection_string == "postgresql://user:pass@localhost:5432/test"

    def test_add_documents(self, mock_pgvector, mock_embedding_model):
        """Test adding documents."""
        # Setup mock
        mock_pgvector.add_documents.return_value = None

        # Create documents
        docs = [
            Document(page_content="Test content 1", metadata={}),
            Document(page_content="Test content 2", metadata={}),
        ]
        user_id = "test_user"
        metadata = {"source": "test"}

        # Create service and add documents
        service = PostgresVectorStoreService(mock_embedding_model)
        doc_id = service.add_documents(docs, user_id, metadata)

        # Verify document_id was generated and metadata was added
        assert len(doc_id) > 0  # UUID was generated
        for doc in docs:
            assert doc.metadata["document_id"] == doc_id
            assert doc.metadata["user_id"] == user_id
            assert doc.metadata["source"] == "test"

        # Verify add_documents was called on PGVector
        mock_pgvector.add_documents.assert_called_once_with(docs)

    def test_search_documents(self, mock_pgvector, mock_embedding_model):
        """Test searching documents."""
        # Setup mock
        mock_pgvector.similarity_search.return_value = [
            Document(page_content="Result 1", metadata={}),
            Document(page_content="Result 2", metadata={}),
        ]

        # Create service and search
        service = PostgresVectorStoreService(mock_embedding_model)
        results = service.search_documents("test query", "test_user", k=2)

        # Verify similarity_search was called correctly
        mock_pgvector.similarity_search.assert_called_once_with("test query", k=2, filter={"user_id": "test_user"})
        assert len(results) == 2

    def test_get_document_count_with_user_id(self, mock_pgvector, mock_embedding_model):
        """Test getting document count for a specific user."""
        # Setup mock for list_documents
        mock_docs = [Mock(), Mock(), Mock()]  # 3 documents
        mock_pgvector._collection.list_documents.return_value = mock_docs

        # Create service and get count
        service = PostgresVectorStoreService(mock_embedding_model)
        count = service.get_document_count(user_id="test_user")

        # Verify correct filter was used and count is returned
        mock_pgvector._collection.list_documents.assert_called_once_with(filter={"user_id": "test_user"})
        assert count == 3

    def test_get_document_count_without_user_id(self, mock_pgvector, mock_embedding_model):
        """Test getting total document count across all users."""
        # Setup mock for list_documents
        mock_docs = [Mock(), Mock(), Mock(), Mock(), Mock()]  # 5 documents
        mock_pgvector._collection.list_documents.return_value = mock_docs

        # Create service and get count
        service = PostgresVectorStoreService(mock_embedding_model)
        count = service.get_document_count()

        # Verify no filter was used and count is returned
        mock_pgvector._collection.list_documents.assert_called_once_with()
        assert count == 5

    def test_delete_document(self, mock_pgvector, mock_embedding_model):
        """Test deleting documents."""
        # Setup service and delete document
        service = PostgresVectorStoreService(mock_embedding_model)
        service.delete_document("doc123", "user456")

        # Verify delete was called with correct filter
        mock_pgvector.delete.assert_called_once_with(filter={"document_id": "doc123", "user_id": "user456"})

    def test_clear_with_user_id(self, mock_pgvector, mock_embedding_model):
        """Test clearing documents for a specific user."""
        # Setup service and clear for user
        service = PostgresVectorStoreService(mock_embedding_model)
        service.clear(user_id="test_user")

        # Verify delete was called with correct filter
        mock_pgvector.delete.assert_called_once_with(filter={"user_id": "test_user"})

    def test_clear_all(self, mock_pgvector, mock_embedding_model):
        """Test clearing all documents."""
        # Setup service
        service = PostgresVectorStoreService(mock_embedding_model)

        # Mock PGVector instance for recreation
        new_pgvector = Mock()
        mock_pgvector_class = Mock(return_value=new_pgvector)

        # When delete_collection is called, prepare for a new instance
        with patch("app.services.postgres_vector_store.PGVector", mock_pgvector_class):
            # Clear all documents
            service.clear()

            # Verify delete_collection was called
            mock_pgvector._collection.delete_collection.assert_called_once()

            # Verify a new PGVector instance was created with correct parameters
            mock_pgvector_class.assert_called_once_with(
                embeddings=service.embedding_model,
                collection_name=service.collection_name,
                connection=service.connection_string,
                pre_delete_collection=False,
            )

            # Verify vector store was updated to the new instance
            assert service.vector_store == new_pgvector
