import os
from unittest.mock import patch, <PERSON><PERSON>

import pytest

from app.services.vector_store import VectorStoreService
from app.services.postgres_vector_store import PostgresVectorStoreService
from app.services.vector_store_factory import create_vector_store


class TestVectorStoreFactory:
    """Tests for the vector_store_factory module."""

    @pytest.fixture(autouse=True)
    def mock_openai_embeddings(self):
        """Mock OpenAIEmbeddings to avoid API calls."""
        with patch("app.services.vector_store.OpenAIEmbeddings") as mock:
            # Return a mock embedding model
            mock.return_value = Mock()
            yield mock

    @pytest.fixture(autouse=True)
    def mock_pgvector(self):
        """Mock PGVector to avoid DB connections."""
        with patch("app.services.postgres_vector_store.PGVector") as mock:
            yield mock

    def test_create_chroma_store_by_default(self):
        """Test that Chroma is used by default."""
        # Mock Chroma
        with patch("app.services.vector_store.Chroma") as mock_chroma:
            # Ensure VECTOR_STORE_TYPE is not set
            with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}, clear=True):
                # Call the factory
                store = create_vector_store()

                # Verify Chroma was used
                mock_chroma.assert_called_once()
                assert isinstance(store, VectorStoreService)

    def test_create_chroma_store_explicitly(self):
        """Test that Chroma is used when explicitly requested."""
        # Mock Chroma
        with patch("app.services.vector_store.Chroma") as mock_chroma:
            # Set VECTOR_STORE_TYPE to chroma
            with patch.dict(
                os.environ,
                {"VECTOR_STORE_TYPE": "chroma", "OPENAI_API_KEY": "test-key"},
            ):
                # Call the factory
                store = create_vector_store()

                # Verify Chroma was used
                mock_chroma.assert_called_once()
                assert isinstance(store, VectorStoreService)

    def test_create_postgres_store(self):
        """Test that PostgreSQL store is used when requested."""
        # Set required environment variables
        with patch.dict(
            os.environ,
            {
                "VECTOR_STORE_TYPE": "postgres",
                "DATABASE_URL": "postgresql://user:pass@localhost:5432/test",
                "OPENAI_API_KEY": "test-key",
            },
        ):
            # Set up mocks
            postgres_store = Mock(spec=PostgresVectorStoreService)

            with patch(
                "app.services.vector_store_factory.PostgresVectorStoreService",
                return_value=postgres_store,
            ):
                # Call the factory
                store = create_vector_store()

                # Verify the result
                assert store == postgres_store

    def test_postgres_without_db_url_raises_error(self):
        """Test that requesting PostgreSQL without DATABASE_URL raises an error."""
        # Set VECTOR_STORE_TYPE to postgres but no DATABASE_URL
        with patch.dict(
            os.environ,
            {"VECTOR_STORE_TYPE": "postgres", "OPENAI_API_KEY": "test-key"},
            clear=True,
        ):
            # Expect an error when calling the factory
            with pytest.raises(ValueError) as exc_info:
                create_vector_store()

            # Verify the error message
            assert "DATABASE_URL environment variable is not set" in str(exc_info.value)

    def test_passing_embedding_model(self):
        """Test that embedding model is passed to the vector store."""
        # Mock embedding model
        embedding_model = Mock()

        # Test with Chroma
        with patch("app.services.vector_store_factory.VectorStoreService") as mock_chroma:
            with patch.dict(
                os.environ,
                {"VECTOR_STORE_TYPE": "chroma", "OPENAI_API_KEY": "test-key"},
            ):
                create_vector_store(embedding_model)
                mock_chroma.assert_called_once_with(embedding_model=embedding_model)

        # Test with PostgreSQL
        with patch("app.services.vector_store_factory.PostgresVectorStoreService") as mock_postgres:
            with patch.dict(
                os.environ,
                {
                    "VECTOR_STORE_TYPE": "postgres",
                    "DATABASE_URL": "postgresql://user:pass@localhost:5432/test",
                    "OPENAI_API_KEY": "test-key",
                },
            ):
                create_vector_store(embedding_model)
                mock_postgres.assert_called_once_with(embedding_model=embedding_model)
