import os
import uuid
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock
import pytest_asyncio

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.outputs import LLMResult, Generation

from app.services.llm_trace_callback import PostgresTraceCallbackHandler
from app.services.llm_trace_service import LLMTraceService
from app.schemas.llm_trace_schema import TraceStatus, LLMTraceFilter

# Use the environment variable for testing
TEST_POSTGRES_URL = os.getenv("TEST_POSTGRES_URL")


@pytest_asyncio.fixture(scope="function", autouse=True)
async def setup_llm_traces_table():
    """Ensure the llm_traces table exists in the test database before any tests run."""
    test_db_url = os.getenv("TEST_POSTGRES_URL")
    if not test_db_url:
        pytest.skip("TEST_POSTGRES_URL not set", allow_module_level=True)
    service = LLMTraceService(postgres_url=test_db_url)
    await service.initialize()


@pytest.fixture
def trace_service():
    """Create a real trace service for integration tests."""
    test_db_url = os.getenv("TEST_POSTGRES_URL")
    if not test_db_url:
        pytest.skip("TEST_POSTGRES_URL not set", allow_module_level=True)
    return LLMTraceService(postgres_url=test_db_url)


@pytest.fixture
def callback_handler(trace_service):
    """Create a PostgresTraceCallbackHandler with our test service."""
    handler = PostgresTraceCallbackHandler(
        trace_service=trace_service,
        user_id="test_user",
        session_id="test_session",
        tags=["test"],
    )
    handler._is_initialized = True
    return handler


@pytest.mark.asyncio
async def test_callback_on_llm_start_end(callback_handler, trace_service):
    """Test the full LLM lifecycle with callbacks."""
    # Define a run_id for tracking
    run_id = str(uuid.uuid4())

    # Set up the LLM start serialized object
    serialized = {
        "id": ["google/gemini-pro"],
        "kwargs": {"temperature": 0.2, "top_p": 0.95},
    }

    # Prompt
    prompt = "What is the capital of France?"

    # Start the LLM call
    await callback_handler.on_llm_start(
        serialized=serialized,
        prompts=[prompt],
        run_id=run_id,
        tags=["question"],
        metadata={"source": "test"},
    )

    # Verify the trace was stored in the active_traces dict
    assert run_id in callback_handler.active_traces
    assert callback_handler.active_traces[run_id]["prompt"] == prompt
    assert callback_handler.active_traces[run_id]["model"] == "gemini-pro"
    assert callback_handler.active_traces[run_id]["provider"] == "google"

    # Set up a mock LLM result
    generation = Generation(text="The capital of France is Paris.")
    result = LLMResult(
        generations=[[generation]],
        llm_output={
            "token_usage": {
                "prompt_tokens": 10,
                "completion_tokens": 8,
                "total_tokens": 18,
            }
        },
    )

    # End the LLM call
    await callback_handler.on_llm_end(response=result, run_id=run_id)

    # Query the DB for the trace
    traces, count = await trace_service.search_traces(LLMTraceFilter(user_id="test_user"))
    found = any(t.prompt == prompt and t.response == "The capital of France is Paris." for t in traces)
    assert found


@pytest.mark.asyncio
async def test_callback_on_chat_model_start(callback_handler):
    """Test callbacks for chat model format."""
    # Define a run_id for tracking
    run_id = "test_chat_123"

    # Set up the chat model start serialized object
    serialized = {"id": ["google/gemini-pro"], "kwargs": {"temperature": 0.0}}

    # Messages
    messages = [
        [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content="What is the capital of France?"),
        ]
    ]

    # Start the chat model call
    await callback_handler.on_chat_model_start(
        serialized=serialized,
        messages=messages,
        run_id=run_id,
        tags=["chat"],
        metadata={"source": "test_chat"},
    )

    # Verify the trace was stored with structured messages
    assert run_id in callback_handler.active_traces

    # Verify prompt is correctly structured
    prompt = callback_handler.active_traces[run_id]["prompt"]
    assert isinstance(prompt, list)
    assert len(prompt) == 2
    assert prompt[0]["role"] == "system"
    assert prompt[0]["content"] == "You are a helpful assistant."
    assert prompt[1]["role"] == "human"
    assert prompt[1]["content"] == "What is the capital of France?"


@pytest.mark.asyncio
async def test_callback_on_llm_error(callback_handler, trace_service):
    """Test callbacks for LLM errors."""
    # Define a run_id for tracking
    run_id = str(uuid.uuid4())

    # Set up the LLM start first
    serialized = {"id": ["google/gemini-pro"], "kwargs": {"temperature": 0.0}}

    # Prompt
    prompt = "This will cause an error"

    # Start the LLM call
    await callback_handler.on_llm_start(serialized=serialized, prompts=[prompt], run_id=run_id)

    # Verify the start was recorded
    assert run_id in callback_handler.active_traces

    # Create an error
    test_error = ValueError("Test LLM error")

    # Trigger the error handler
    await callback_handler.on_llm_error(error=test_error, run_id=run_id)

    # Query the DB for the trace
    traces, count = await trace_service.search_traces(LLMTraceFilter(user_id="test_user"))
    found = any(t.prompt == prompt and t.status == TraceStatus.ERROR for t in traces)
    assert found


@pytest.mark.asyncio
async def test_service_auto_initialization(monkeypatch):
    """Test that the handler can initialize the trace service on demand."""
    # Create a mock service to be returned by the constructor
    mock_service = MagicMock(spec=LLMTraceService)
    mock_service.initialize = AsyncMock()

    # Patch the LLMTraceService constructor to return our mock
    monkeypatch.setattr(
        "app.services.llm_trace_callback.LLMTraceService",
        MagicMock(return_value=mock_service),
    )

    # Create a handler with no trace_service, only URL
    handler = PostgresTraceCallbackHandler(postgres_url=TEST_POSTGRES_URL, user_id="test_user")

    # The handler shouldn't be initialized yet
    assert handler._is_initialized is False

    # Now call _ensure_initialized which should create and init the service
    await handler._ensure_initialized()

    # Verify the service was created and initialized
    assert handler._is_initialized is True
    assert handler.trace_service is mock_service
    mock_service.initialize.assert_called_once()


@pytest.mark.asyncio
async def test_sync_handlers_create_async_tasks():
    """Test that sync handlers correctly handle trace data storage."""
    # Create a handler with mocked trace service
    mock_service = MagicMock()
    handler = PostgresTraceCallbackHandler(trace_service=mock_service, user_id="test_user")

    # Call the sync version of on_llm_start
    handler.on_llm_start_sync(serialized={"id": ["test/model"]}, prompts=["test"], run_id="sync_test")

    # Verify trace data was stored
    assert "sync_test" in handler.active_traces
    assert handler.active_traces["sync_test"]["prompt"] == "test"
    assert handler.active_traces["sync_test"]["provider"] == "test"
    assert handler.active_traces["sync_test"]["model"] == "model"
