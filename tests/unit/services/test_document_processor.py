import os
from unittest.mock import MagicMock, patch

from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter

from app.schemas.document_schema import DocumentType
from app.schemas.llm_config_schema import LLMTask
from app.services.document_processor import DocumentProcessor


class TestDocumentProcessor:
    """Test suite for DocumentProcessor service."""

    def test_init(self, create_mock_llm_config_service):
        """Test that DocumentProcessor initializes correctly."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Verify that the config service was used correctly
        create_mock_llm_config_service.get_chunking_params.assert_called_once_with(LLMTask.DOCUMENT_EXTRACTION)

        # Verify text splitter was initialized
        assert isinstance(processor.text_splitter, RecursiveCharacterTextSplitter)

        # Verify Gemini model was initialized
        assert processor.gemini_model is not None

    def test_get_doc_type(self, create_mock_llm_config_service):
        """Test that file types are correctly identified."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Test various file extensions
        assert processor._get_doc_type("pdf") == DocumentType.PDF
        assert processor._get_doc_type("docx") == DocumentType.DOCX
        assert processor._get_doc_type("doc") == DocumentType.DOCX
        assert processor._get_doc_type("txt") == DocumentType.TXT
        assert processor._get_doc_type("jpg") == DocumentType.IMAGE
        assert processor._get_doc_type("jpeg") == DocumentType.IMAGE
        assert processor._get_doc_type("png") == DocumentType.IMAGE
        assert processor._get_doc_type("gif") == DocumentType.IMAGE
        assert processor._get_doc_type("webp") == DocumentType.IMAGE
        assert processor._get_doc_type("json") == DocumentType.JSON

        # Test fallback to TXT for unknown extensions
        assert processor._get_doc_type("unknown") == DocumentType.TXT

    def test_get_file_extension(self, create_mock_llm_config_service):
        """Test file extension extraction."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        assert processor._get_file_extension("document.pdf") == "pdf"
        assert processor._get_file_extension("path/to/file.docx") == "docx"
        assert processor._get_file_extension("file_without_extension") == "file_without_extension"
        assert processor._get_file_extension("file.with.multiple.dots.txt") == "txt"

    def test_save_temp_file(self, create_mock_llm_config_service):
        """Test temporary file creation."""
        processor = DocumentProcessor(create_mock_llm_config_service)
        content = b"Test content"
        extension = "txt"

        # Call the method
        temp_file_path = processor._save_temp_file(content, extension)

        try:
            # Verify the file was created
            assert os.path.exists(temp_file_path)
            assert temp_file_path.endswith(".txt")

            # Verify correct content
            with open(temp_file_path, "rb") as f:
                saved_content = f.read()
                assert saved_content == content

        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    @patch("app.services.document_processor.GeminiTextExtractor")
    def test_process_file_pdf(self, mock_gemini_extractor, create_mock_llm_config_service, sample_pdf_file):
        """Test processing a PDF file through the main interface."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Mock the Gemini text extractor
        mock_extractor_instance = mock_gemini_extractor.return_value
        mock_extractor_instance.extract_text.return_value = "PDF content"

        # Mock the text splitter
        processor.text_splitter = MagicMock()
        processed_docs = [
            Document(page_content="Chunk 1", metadata={"page": 1}),
            Document(page_content="Chunk 2", metadata={"page": 1}),
        ]
        processor.text_splitter.split_documents.return_value = processed_docs

        # Call the method
        docs, metadata = processor.process_file(sample_pdf_file, "document.pdf")

        # Verify the extractor was called correctly
        mock_extractor_instance.extract_text.assert_called_once_with(sample_pdf_file, "document.pdf", DocumentType.PDF)

        # Verify the text splitter was called
        processor.text_splitter.split_documents.assert_called_once()

        # Verify the metadata
        assert metadata["doc_type"] == DocumentType.PDF
        assert "extraction_method" in metadata
        assert metadata["extraction_method"] == "llm"

    @patch("app.services.document_processor.GeminiTextExtractor")
    def test_process_file_docx(self, mock_gemini_extractor, create_mock_llm_config_service):
        """Test processing a DOCX file through the main interface."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Mock the Gemini text extractor
        mock_extractor_instance = mock_gemini_extractor.return_value
        mock_extractor_instance.extract_text.return_value = "DOCX content"

        # Mock the text splitter
        processor.text_splitter = MagicMock()
        processed_docs = [
            Document(page_content="Chunk 1", metadata={}),
        ]
        processor.text_splitter.split_documents.return_value = processed_docs

        # Call the method
        docs, metadata = processor.process_file(b"content", "document.docx")

        # Verify the extractor was called correctly
        mock_extractor_instance.extract_text.assert_called_once_with(b"content", "document.docx", DocumentType.DOCX)

        # Verify the text splitter was called
        processor.text_splitter.split_documents.assert_called_once()

        # Verify the metadata
        assert metadata["doc_type"] == DocumentType.DOCX
        assert "extraction_method" in metadata
        assert metadata["extraction_method"] == "llm"

    @patch("app.services.document_processor.GeminiTextExtractor")
    def test_process_file_txt(self, mock_gemini_extractor, create_mock_llm_config_service):
        """Test processing a text file through the main interface."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Mock the Gemini text extractor
        mock_extractor_instance = mock_gemini_extractor.return_value
        mock_extractor_instance.extract_text.return_value = "Text content"

        # Mock the text splitter
        processor.text_splitter = MagicMock()
        processed_docs = [
            Document(page_content="Chunk 1", metadata={}),
        ]
        processor.text_splitter.split_documents.return_value = processed_docs

        # Call the method
        docs, metadata = processor.process_file(b"content", "document.txt")

        # Verify the extractor was called correctly
        mock_extractor_instance.extract_text.assert_called_once_with(b"content", "document.txt", DocumentType.TXT)

        # Verify the text splitter was called
        processor.text_splitter.split_documents.assert_called_once()

        # Verify the metadata
        assert metadata["doc_type"] == DocumentType.TXT
        assert "extraction_method" in metadata
        assert metadata["extraction_method"] == "llm"

    def test_process_text_content(self, create_mock_llm_config_service):
        """Test processing raw text."""
        processor = DocumentProcessor(create_mock_llm_config_service)

        # Mock text_splitter behavior
        processor.text_splitter = MagicMock()
        processed_docs = [Document(page_content="Chunk 1", metadata={"source": "test"})]
        processor.text_splitter.split_documents.return_value = processed_docs

        # Call the method
        text = "This is test content for processing."
        metadata = {"source": "test"}
        result = processor.process_text(text, metadata)

        # Verify results
        assert result == processed_docs

        # Verify text_splitter was called with correct document
        call_args = processor.text_splitter.split_documents.call_args[0][0]
        assert len(call_args) == 1
        assert call_args[0].page_content == text
        assert call_args[0].metadata == metadata
