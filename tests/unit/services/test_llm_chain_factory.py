import os
import pytest
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch

from langchain_core.prompts import PromptTemplate, ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>rOutputParser
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>, Conversation<PERSON>hain

from app.services.llm_chain_factory import LL<PERSON>hainFactory
from app.services.llm_trace_service import LLMTraceService
from app.services.llm_trace_callback import PostgresTraceCallbackHandler
from app.schemas.llm_config_schema import LLMProvider, ModelName

# Use the environment variable for testing
TEST_POSTGRES_URL = os.getenv("TEST_POSTGRES_URL")


@pytest.fixture
def trace_service():
    """Create a mock trace service."""
    service = MagicMock(spec=LLMTraceService)
    service.initialize = AsyncMock()
    return service


@pytest.fixture
def chain_factory(trace_service):
    """Create a chain factory with a mock trace service."""
    return LLMChainFactory(
        postgres_url=TEST_POSTGRES_URL,
        trace_service=trace_service,
        default_model=ModelName.GEMINI_20_FLASH.value,
        default_provider=LLMProvider.GOOGLE.value,
    )


@pytest.mark.asyncio
async def test_initialize(chain_factory, trace_service):
    """Test initializing the chain factory."""
    # Check initial state
    assert chain_factory._trace_service_initialized is True  # Should be already initialized with our mock

    # Force re-initialization with a new mock
    chain_factory._trace_service_initialized = False
    chain_factory.trace_service = None

    # Create a new mock service
    new_service = MagicMock(spec=LLMTraceService)
    new_service.initialize = AsyncMock()

    # Mock the LLMTraceService constructor to return our mock
    with patch("app.services.llm_chain_factory.LLMTraceService", return_value=new_service) as mock_constructor:
        # Call initialize
        await chain_factory.initialize()

        # Verify the service was created and initialized
        mock_constructor.assert_called_once_with(postgres_url=TEST_POSTGRES_URL)
        new_service.initialize.assert_called_once()
        assert chain_factory._trace_service_initialized is True


def test_create_callback_handler(chain_factory, trace_service):
    """Test creating a callback handler."""
    # Create a handler
    handler = chain_factory._create_callback_handler(user_id="test_user", session_id="test_session", tags=["test"])

    # Verify it was created with the correct attributes
    assert isinstance(handler, PostgresTraceCallbackHandler)
    assert handler.trace_service is trace_service
    assert handler.user_id == "test_user"
    assert handler.session_id == "test_session"
    assert "test" in handler.tags


def test_create_llm(chain_factory):
    """Test creating an LLM with tracing."""
    # Mock the callback handler creation
    mock_handler = MagicMock(spec=PostgresTraceCallbackHandler)
    mock_handler.tags = ["test"]  # Add tags attribute to the mock
    chain_factory._create_callback_handler = MagicMock(return_value=mock_handler)

    # Create an LLM
    llm = chain_factory.create_llm(
        model_name="gemini-pro",
        provider="google",
        temperature=0.5,
        user_id="test_user",
        tags=["test"],
        task="question_answering",
    )

    # Verify the LLM was created correctly
    assert llm is not None
    assert "gemini-pro" in llm.model  # Check if the model name contains the expected value
    assert llm.temperature == 0.5

    # Verify the callback handler was created and used
    chain_factory._create_callback_handler.assert_called_once()
    assert mock_handler in llm.callbacks

    # Verify task tag was added
    assert f"task:question_answering" in mock_handler.tags

    # Test with a task enum
    handler2 = MagicMock(spec=PostgresTraceCallbackHandler)
    handler2.tags = []  # Add tags attribute to the mock
    chain_factory._create_callback_handler = MagicMock(return_value=handler2)

    llm2 = chain_factory.create_llm(
        temperature=0.0,  # Test defaults
        task="QUESTION_ANSWERING",  # Different capitalization/format
    )

    # Verify defaults were used
    assert ModelName.GEMINI_20_FLASH.value in llm2.model
    assert "task:QUESTION_ANSWERING" in handler2.tags


def test_create_chain_with_string_prompt(chain_factory):
    """Test creating a chain from a string prompt."""
    # Create a simple chain with a string prompt
    chain = chain_factory.create_chain(prompt="Tell me about {topic}", user_id="test_user")

    # Verify it's an LLMChain with the correct prompt structure
    assert isinstance(chain, LLMChain)
    assert isinstance(chain.prompt, PromptTemplate)
    assert chain.prompt.template == "Tell me about {topic}"
    assert "topic" in chain.prompt.input_variables


def test_create_chain_with_prompt_template(chain_factory):
    """Test creating a chain with a PromptTemplate."""
    # Create a prompt template
    prompt = PromptTemplate.from_template("You are a helpful assistant.\n\nUser question: {question}\n\nAnswer:")

    # Create a chain with the template
    chain = chain_factory.create_chain(prompt=prompt, user_id="test_user", temperature=0.2)

    # Verify the chain has the correct prompt
    assert isinstance(chain, LLMChain)
    assert chain.prompt is prompt
    assert chain.llm.temperature == 0.2


def test_create_chain_with_chat_prompt(chain_factory):
    """Test creating a chain with a ChatPromptTemplate."""
    # Create a chat prompt template
    chat_prompt = ChatPromptTemplate.from_messages(
        [("system", "You are a helpful assistant."), ("human", "{question}")]
    )

    # Create a chain with the chat template
    chain = chain_factory.create_chain(prompt=chat_prompt, user_id="test_user")

    # Verify the chain has the correct prompt
    assert isinstance(chain, LLMChain)
    assert chain.prompt is chat_prompt


def test_create_chain_with_output_parser(chain_factory):
    """Test creating a chain with an output parser."""
    # Simple prompt template
    prompt = PromptTemplate.from_template("List {n} {items}:")

    # Create a chain with an output parser
    parser = StrOutputParser()
    chain = chain_factory.create_chain(prompt=prompt, output_parser=parser, user_id="test_user")

    # Verify it created an LCEL sequence with the parser
    assert not isinstance(chain, LLMChain)  # Should be a RunnableSequence instead

    # It's difficult to test RunnableSequence internals directly
    # Verify the chain contains our components in some way
    chain_str = str(chain)
    assert "StrOutputParser" in chain_str
    assert "List {n} {items}" in chain_str


def test_create_conversation_chain(chain_factory):
    """Test creating a conversation chain."""
    # Create a conversation chain
    chain = chain_factory.create_conversation_chain(
        user_id="test_user", model_name="gemini-pro", provider="google", temperature=0.7
    )

    # Verify it's a ConversationChain
    assert isinstance(chain, ConversationChain)
    assert "gemini-pro" in chain.llm.model  # Check if the model string contains gemini-pro
    assert chain.llm.temperature == 0.7

    # Verify it has a memory component
    assert chain.memory is not None
