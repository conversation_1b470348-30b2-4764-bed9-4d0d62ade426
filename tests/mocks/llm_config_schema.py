from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class LLMProvider(str, Enum):
    """LLM providers supported by the system"""

    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROK = "grok"


# Provider mapping for ModelName - initialized before the class definition
_provider_map: Dict[str, LLMProvider] = {}


class ModelName(str, Enum):
    """Available LLM models organized by provider - simplified for testing"""

    # OpenAI models
    GPT_4 = "gpt-4"
    GPT_35_TURBO = "gpt-3.5-turbo"

    # Anthropic models
    CLAUDE_3 = "claude-3"

    # Google models
    GEMINI_PRO = "gemini-pro"

    # Grok models
    GROK_1 = "grok-1"

    @property
    def provider(self) -> Optional[LLMProvider]:
        """Get the provider for this model"""
        return _provider_map.get(self)


# Initialize the model provider mapping after ModelName is defined
_provider_map.update(
    {
        # OpenAI models
        ModelName.GPT_4: LL<PERSON>rovider.OPENAI,
        ModelName.GPT_35_TURBO: LLMProvider.OPENAI,
        # Anthropic models
        ModelName.CLAUDE_3: LLMProvider.ANTHROPIC,
        # Google models
        ModelName.GEMINI_PRO: LLMProvider.GOOGLE,
        # Grok models
        ModelName.GROK_1: LLMProvider.GROK,
    }
)


class LLMTask(str, Enum):
    """Tasks that can be performed using LLMs"""

    QUESTION_ANSWERING = "question_answering"
    LAWSUIT_EXTRACTION = "lawsuit_extraction"
    DOCUMENT_EXTRACTION = "document_extraction"


class ModelConfig(BaseModel):
    """Configuration for a specific LLM model"""

    provider: LLMProvider
    temperature: float = Field(0.0, description="Temperature setting for the model (0.0-1.0)")
    max_tokens: Optional[int] = Field(None, description="Maximum number of tokens for the response")
    additional_params: Dict = Field(default_factory=dict, description="Additional provider-specific parameters")


class ProviderConfig(BaseModel):
    """Configuration for a specific LLM provider"""

    api_key_env: str = Field(..., description="Environment variable name for the API key")
    default_model: ModelName = Field(..., description="Default model to use for this provider")


# Helper functions for easy access to models by provider
def get_models_for_provider(provider: LLMProvider) -> List[ModelName]:
    """Get all models for a specific provider"""
    return [model for model in ModelName if model.provider == provider]


# Map to easily look up available models for each provider
PROVIDER_MODELS = {provider: get_models_for_provider(provider) for provider in LLMProvider}


def get_model_key(provider: LLMProvider, model: ModelName) -> str:
    """Create a model key from provider and model"""
    return f"{provider}:{model}"
