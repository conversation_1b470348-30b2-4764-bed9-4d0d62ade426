<p align="center">
  <img src="assets/solo_circuit.png" alt="SoloCircuit Logo" width="400">
</p>

# SoloCircuit API

A FastAPI-based web service with three endpoints for document processing, semantic search, and structured data extraction.

## Features

- **Document Ingestion**: Upload files (PDF, Word, images) or text data for processing and embedding
- **Semantic Search**: Query your documents using natural language and get relevant answers
- **Structured Data Extraction**: Generate structured lawsuit data from your documents
- **JWT Authentication**: Secure API endpoints with JSON Web Token authentication

## Setup

### Prerequisites

- Python 3.9+
- [Optional] Tesseract OCR (for image text extraction)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/solo_circuit.git
   cd solo_circuit
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file with your API keys (see `.env.example` for reference):
   ```
   cp .env.example .env
   # Edit .env with your API keys
   ```

### Environment Variables

The application requires the following environment variables:

```
# API Keys for various LLM providers
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
# ... other LLM provider keys as needed

# JWT Authentication (required)
JWT_SECRET_KEY=your-very-secure-secret-key-here
JWT_ALGORITHM=HS256  # Optional, defaults to HS256

# Vector Store Configuration (optional)
VECTOR_STORE_TYPE=chroma  # Options: chroma, postgres
CHROMA_PERSIST_DIRECTORY=./data/chroma  # Only used if VECTOR_STORE_TYPE=chroma

# PostgreSQL Configuration (required if VECTOR_STORE_TYPE=postgres)
DATABASE_URL=postgresql://user:password@localhost:5432/vectordb
PGVECTOR_COLLECTION=document_embeddings
```

For more details on PostgreSQL with pgvector integration, see [POSTGRES_SETUP.md](POSTGRES_SETUP.md).

### Running the API

Start the development server:
```
uvicorn app.main:app --reload
```

The API will be available at http://localhost:8000. The interactive documentation is at http://localhost:8000/docs.

### Development Mode - Skipping Authentication

For local development and testing, you can skip JWT authentication by setting the `SKIP_AUTH` environment variable to `true`:

```bash
# In .env file
SKIP_AUTH=true
```

Or when running the server:
```bash
SKIP_AUTH=true uvicorn app.main:app --reload
```

A convenient script is also provided to start the development server with authentication disabled:

```bash
./scripts/dev_server.sh
```

**WARNING:** This should only be used for local development and testing, never in production environments.

### Token Generation Script

A script is provided to easily generate JWT tokens for development and testing:

```bash
python scripts/generate_token.py
```

This will print a JWT token that can be used for authentication, along with instructions on how to use it.

## Authentication

The API uses JWT (JSON Web Token) authentication to secure all endpoints (except for `/health` and `/` which remain public).

### Authentication Headers

All API requests must include a valid JWT token in the Authorization header:

```
Authorization: Bearer your-jwt-token-here
```

### Generate a JWT Token

You can generate a JWT token using the JWT library in various languages. Here's an example in Python:

```python
import jwt
import os

# Generate a token with minimal payload
payload = {"app_name": "my-app"}
secret_key = os.getenv("JWT_SECRET_KEY")

# Create the token
token = jwt.encode(payload, secret_key, algorithm="HS256")
print(f"Your JWT token: {token}")
```

For more details on authentication setup, see the [JWT Authentication Documentation](docs/JWT_AUTHENTICATION.md).

## API Endpoints

### Document Management

#### POST /api/documents
Upload a document for processing and embedding. Accepts either:
- A file upload (see supported formats below)
- A JSON object with text content

##### Supported File Formats
- **Documents**
  - PDF (`.pdf`)
  - Microsoft Word (`.doc`, `.docx`)
  - Plain Text (`.txt`)
- **Images** (with OCR text extraction)
  - JPEG (`.jpg`, `.jpeg`)
  - PNG (`.png`)
  - WebP (`.webp`)
  - GIF (`.gif`) - recognized but not optimized for text extraction
- **Other Formats**
  - JSON (`.json`)
  - Other files will be processed using the `UnstructuredFileLoader` as a fallback, which may provide varying results

All documents are associated with a specific user_id, which is required for all operations.

Example with curl:
```bash
# Upload a document with text content
curl -X POST "http://localhost:8000/api/documents" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "user_id": "user123",
    "text": "This is the document content to process",
    "llm_provider": "google",
    "llm_model": "gemini-2.5-flash-preview-04-17"
  }'

# Example Response:
# {
#   "document_id": "doc_9a72ec6b3ad94f1f8c23",
#   "chunks_stored": 1,
#   "user_id": "user123",
#   "status": "success"
# }

# For file uploads, use the dedicated endpoint
curl -X POST "http://localhost:8000/api/upload-file" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "user_id=user123" \
  -F "file=@your_document.pdf" \
  -F "llm_provider=google" \
  -F "llm_model=gemini-2.5-flash-preview-04-17"
```

#### DELETE /api/documents
Delete a document by its ID. The user_id and document_id are provided in the JSON request body.

```bash
curl -X DELETE "http://localhost:8000/api/documents" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "document_id": "doc123",
    "user_id": "user123"
  }'

# Example Response:
# {
#   "status": "success",
#   "message": "Document doc123 deleted",
#   "user_id": "user123"
# }
```

#### GET /api/documents/count
Get the total count of embedded documents for a specific user.

```bash
curl -X GET "http://localhost:8000/api/documents/count?user_id=user123" \
  -H "Authorization: Bearer your_jwt_token"

# Example Response:
# {
#   "count": 5,
#   "user_id": "user123"
# }
```

### Query

#### POST /api/query
Query the system with a natural language question. This will only search documents belonging to the specified user.

Example:
```bash
curl -X POST "http://localhost:8000/api/query" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "user_id": "user123",
    "query": "What is the main topic of my documents?",
    "llm_provider": "openai",
    "llm_model": "gpt-4.1"
  }'

# Example Response:
# {
#   "answer": "Based on your documents, the main topic appears to be...",
#   "sources": ["doc_1682fd8362a14298937c", "doc_9a72ec6b3ad94f1f8c23"]
# }
```

You can optionally specify the LLM provider and model using query parameters:

```bash
curl -X POST "http://localhost:8000/api/query?llm_provider=anthropic&llm_model=claude-3-7-sonnet-latest" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "query": "What is the lawsuit amount?"}'
```

### Lawsuit Data

#### POST /api/extract-lawsuit-data
Extract structured lawsuit data from all available documents for a specific user.

Example:
```bash
curl -X POST "http://localhost:8000/api/extract-lawsuit-data" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "config": {"additional_context": "Focus on the customer details"}}'

# Example Response:
# {
#   "lawsuit_exists": true,
#   "lawsuit_state": "California",
#   "lawsuit_amount": "$15,750.00",
#   "civil_number": "CIV-2023-123456",
#   "lawyer_email": "<EMAIL>",
#   "law_firm": {
#     "id": 42,
#     "name": "Smith & Associates"
#   },
#   "plaintiff": {
#     "id": 123,
#     "name": "ABC Collections Inc."
#   },
#   "customer": {
#     "name": "John Doe",
#     "phone_number": "************",
#     "mailing_address": {
#       "street": "123 Main St",
#       "city": "San Francisco",
#       "state": "California",
#       "zip_code": "94107"
#     }
#   }
# }
```

## Testing

Solo Circuit has comprehensive test coverage using Pytest. The test suite includes unit tests, integration tests, and schema validation tests.

### Running Tests

To run the tests, first install the development dependencies:

```bash
pip install -r requirements-dev.txt
```

Then run the tests with Pytest:

```bash
# Run all tests
pytest

# Run with coverage report
pytest --cov=app

# Run only unit tests
pytest tests/unit/

# Run only integration tests
pytest -m integration

# Run only fast tests
pytest -m "not slow"
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
  - `tests/unit/services/`: Tests for service components
  - `tests/unit/api/`: Tests for API endpoints
  - `tests/unit/schemas/`: Tests for data models and schemas
- **Integration Tests**: Test component interactions
  - `tests/integration/`: End-to-end workflow tests

### Writing Tests

When adding new features, please include appropriate tests. Follow these guidelines:

1. Use fixtures from `tests/conftest.py` for common test dependencies
2. Mock external services and dependencies
3. Use parametrized tests for testing multiple scenarios
4. Use appropriate markers (`@pytest.mark.integration`, `@pytest.mark.slow`, etc.)
