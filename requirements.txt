aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.50.0
anyio==4.9.0
asgiref==3.8.1
asyncpg==0.30.0
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
black==25.1.0
build==1.2.2.post1
CacheControl==0.14.2
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.6.3
cleo==2.1.0
click==8.1.8
coloredlogs==15.0.1
coverage==7.8.0
crashtest==0.4.1
dataclasses-json==0.6.7
Deprecated==1.2.18
distlib==0.3.9
distro==1.9.0
dnspython==2.7.0
dulwich==0.22.8
durationpy==0.9
ecdsa==0.19.1
email_validator==2.2.0
execnet==2.1.1
faiss-cpu==1.10.0
Faker==37.1.0
fastapi==0.110.0
fastjsonschema==2.21.1
filelock==3.18.0
filetype==1.2.0
findpython==0.6.3
flatbuffers==25.2.10
frozenlist==1.6.0
fsspec==2025.3.2
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc0
google-api-python-client==2.168.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
grpcio==1.71.0
grpcio-status==1.71.0
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.8
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
humanfriendly==10.0
hypothesis==6.131.10
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
installer==0.7.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
keyring==25.6.0
kubernetes==32.0.1
langchain==0.3.24
langchain-anthropic==0.3.12
langchain-community==0.3.22
langchain-core==0.3.56
langchain-google-genai==2.0.10
langchain-openai==0.3.14
langchain-postgres==0.0.14
langchain-text-splitters==0.3.8
langsmith==0.3.33
lxml==5.4.0
markdown-it-py==3.0.0
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
monotonic==1.6
more-itertools==10.7.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.3
mypy==1.15.0
mypy_extensions==1.1.0
numpy==1.26.4
oauthlib==3.2.2
onnxruntime>=1.16.2,<1.20.0
openai==1.76.0
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
orjson==3.10.16
overrides==7.7.0
packaging==24.2
pathspec==0.12.1
pbs-installer==2025.4.9
pdf2image==1.17.0
pgvector==0.3.6
pillow==11.2.1
pip-tools==7.4.1
pkginfo==********
platformdirs==4.3.7
pluggy==1.5.0
poetry==2.1.2
poetry-core==2.1.2
posthog==4.0.0
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psycopg==3.2.6
psycopg-pool==3.2.6
psycopg2-binary==2.9.10
py-cpuinfo==9.0.0
pyasn1==0.4.8
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.11.3
pydantic-settings==2.9.1
pydantic_core==2.33.1
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
pypdf==5.4.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pytesseract==0.3.13
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-benchmark==5.1.0
pytest-cov==6.1.1
pytest-mock==3.14.0
pytest-timeout==2.4.0
pytest-xdist==3.6.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.1.0
python-jose==3.4.0
python-multipart==0.0.9
PyYAML==6.0.2
RapidFuzz==3.13.0
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==14.0.0
rsa==4.9.1
setuptools==80.3.1
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
SQLAlchemy==2.0.40
starlette==0.36.3
sympy==1.13.3
tenacity==9.1.2
tiktoken==0.9.0
tokenizers==0.21.1
tomlkit==0.13.2
tqdm==4.67.1
trove-classifiers==2025.4.11.15
typer==0.15.2
types-cryptography==********
types-jwt==0.1.3
types-Pillow==10.2.0.20240822
types-pyasn1==0.6.0.20250208
types-python-jose==3.4.0.20250224
types-PyYAML==6.0.12.20250402
types-requests==2.32.0.20250328
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.27.1
uvloop==0.21.0
virtualenv==20.30.0
watchfiles==1.0.5
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
wrapt==1.17.2
xattr==1.1.4
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
