# PostgreSQL Vector Store Integration

SoloCircuit now supports using PostgreSQL with pgvector as an alternative to Chroma for vector storage. This document explains how to set up and use the PostgreSQL vector store.

## Overview

PostgreSQL with pgvector provides several advantages for production deployments:

- Persistent database storage
- Horizontal scalability
- Transactional guarantees
- SQL-based filtering and querying
- Integration with existing database workflows

## Configuration

To use PostgreSQL as the vector store, set the following environment variables:

```bash
# Required: Set vector store type to postgres
VECTOR_STORE_TYPE=postgres

# Required: PostgreSQL connection string
DATABASE_URL=postgresql://username:password@hostname:port/database

# Optional: Collection name for pgvector (defaults to document_embeddings)
PGVECTOR_COLLECTION=document_embeddings
```

## Local Development Setup

To use PostgreSQL locally during development:

1. Install PostgreSQL and pgvector:

```bash
# Install PostgreSQL (for Mac)
brew install postgresql

# For Ubuntu
# sudo apt-get install postgresql postgresql-contrib

# Start PostgreSQL service
brew services start postgresql
```

2. Create a database and install the pgvector extension:

```bash
# Connect to PostgreSQL
psql postgres

# Create a database for vector storage
CREATE DATABASE vectordb;

# Connect to the new database
\c vectordb

# Install pgvector extension
CREATE EXTENSION vector;

# Exit psql
\q
```

3. Set the appropriate environment variables in your `.env` file:

```bash
VECTOR_STORE_TYPE=postgres
DATABASE_URL=postgresql://localhost:5432/vectordb
PGVECTOR_COLLECTION=document_embeddings
```

## Heroku Deployment

When deploying to Heroku:

1. The application will automatically use the `DATABASE_URL` provided by Heroku Postgres.
2. The pgvector extension will be automatically installed via the `scripts/setup_pgvector.py` script.
3. Make sure to use a Postgres plan that supports pgvector (Standard tier or higher, PostgreSQL 15+).

## Migrating from Chroma

If you have existing data in Chroma that you want to migrate to PostgreSQL:

1. Set both Chroma and PostgreSQL connection details in your `.env` file
2. Run the migration script:

```bash
python scripts/migrate_chroma_to_postgres.py
```

## Troubleshooting

### Extension Installation Issues

If you encounter issues installing the pgvector extension on Heroku:

1. Verify you're using a Standard tier database or higher
2. Check that you're using PostgreSQL 15 or higher
3. Try running the setup script manually:

```bash
heroku run python scripts/setup_pgvector.py
```

### Connection Issues

If you encounter connection issues:

1. Verify the `DATABASE_URL` is correctly set
2. Ensure the pgvector extension is installed
3. Check that the `langchain_embedding` schema and table are created
4. Ensure you have sufficient permissions on the database 