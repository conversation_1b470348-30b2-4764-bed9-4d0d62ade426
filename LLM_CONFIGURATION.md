# LLM Configuration System

This document explains the LLM configuration system implemented in SoloCircuit API to manage model providers, models, and task-specific settings.

## Overview

The LLM configuration system provides a centralized way to:
- Define and manage which LLM providers and models are available
- Establish clear relationships between providers and their models
- Configure default provider/model combinations for specific tasks
- Set chunking and retrieval parameters for different tasks
- Dynamically resolve model selection based on user requests
- Cache model instances for better performance

## Architecture

The system consists of:

### 1. Configuration Schema (`app/schemas/llm_config_schema.py`)

- **LLMProvider**: Enum of supported LLM providers (OpenAI, Anthropic, Google)
- **ModelName**: Enum of specific model names with provider relationship
  - Each model has a `provider` property that links it to its provider
  - Models are grouped by provider in the code for clarity
- **PROVIDER_MODELS**: Dictionary mapping providers to their available models
- **LLMTask**: Enum of tasks performed using LLMs
- **ModelConfig**: Settings for a specific model (temperature, token limits, etc.)
- **ProviderConfig**: Settings for a provider (API key, default model)
- **TaskConfig**: Task-specific settings (default provider/model, chunking parameters)
- **LLMConfig**: Overall configuration combining all settings

### 2. Configuration Service (`app/services/llm_config_service.py`)

The `LLMConfigService` class:
- Manages the configuration for all LLM operations
- Provides methods to resolve provider and model based on task and user preferences
- Creates, caches, and returns model instances
- Supplies task-specific parameters for operations like text chunking and retrieval
- Validates that models are used with their correct providers

### 3. Integration with Services

- **LLMService**: Uses the config service to determine which models to use for QA and data extraction
- **DocumentProcessor**: Uses the config service for chunking parameters and document processing settings

### 4. API Integration

All API endpoints use the configuration service to:
- Allow users to specify provider/model via query parameters
- Ensure consistent defaults across the application
- Apply the right parameters for specific tasks

## Usage

### 1. Setting Global Defaults

The default configuration is defined in `LLMConfigService._build_default_config()`.

### 2. Task-Specific Configuration

Each task has its own configuration including:
- Default provider/model pair
- Allowed providers
- Chunking parameters
- Retrieval parameters (k)

### 3. Requesting Specific Models

Clients can request specific providers or models:
- Via query parameters in API requests
- Via the `config` field in request bodies

Model selection is intelligent:
- If a specific model is requested, its provider is automatically determined
- If a specific provider is requested, its default model is used
- The system validates that requested models are available for their providers
- If invalid options are requested, the system falls back to defaults

## Benefits

- **Strong Model-Provider Relationship**: Models are explicitly linked to their providers
- **Single Source of Truth**: All configuration in one place
- **Consistency**: Default settings applied consistently
- **Flexibility**: Clients can override defaults as needed
- **Scalability**: Easy to add new providers, models, or tasks
- **Improved Performance**: Model instance caching
- **Type Safety**: Fully typed using Pydantic 