{"name": "SoloCircuit API", "description": "API for document ingestion, semantic search, and structured data extraction", "repository": "https://github.com/yourusername/solocircuit", "keywords": ["python", "<PERSON><PERSON><PERSON>", "langchain", "ai", "llm"], "env": {"ENVIRONMENT": {"description": "Application environment (development, staging, production)", "value": "production", "required": true}, "OPENAI_API_KEY": {"description": "Your OpenAI API key", "required": true}, "ANTHROPIC_API_KEY": {"description": "Your Anthropic API key (optional)", "required": false}, "JWT_SECRET_KEY": {"description": "Secret key for JWT token generation and validation", "required": true}, "JWT_ALGORITHM": {"description": "Algorithm used for JWT", "value": "HS256", "required": false}, "VECTOR_STORE_TYPE": {"description": "Type of vector store to use (postgres or chroma)", "value": "postgres", "required": false}, "PGVECTOR_COLLECTION": {"description": "Collection name for pgvector", "value": "document_embeddings", "required": false}, "LOG_LEVEL": {"description": "Logging level", "value": "INFO", "required": false}}, "buildpacks": [{"url": "heroku/python"}], "addons": [{"plan": "heroku-postgresql:standard-0", "options": {"version": "15"}}], "scripts": {"postdeploy": "python scripts/setup_pgvector.py"}, "formation": {"web": {"quantity": 1, "size": "standard-1x"}}}