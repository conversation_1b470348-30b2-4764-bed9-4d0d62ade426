#!/bin/bash
# Database deployment script for different environments

set -e  # Exit immediately if a command exits with a non-zero status

# Determine environment
ENVIRONMENT=${ENVIRONMENT:-development}
echo "Running deployment in $ENVIRONMENT environment"

# Create and migrate the database
case "$ENVIRONMENT" in
  production)
    echo "Deploying to PRODUCTION database"
    # In production, we want to be more careful
    python database_migration.py --create-db
    ;;
  staging)
    echo "Deploying to STAGING database"
    python database_migration.py --create-db
    ;;
  development|test)
    echo "Deploying to TEST/DEV database"
    python database_migration.py --create-db --force
    ;;
  *)
    echo "Unknown environment: $ENVIRONMENT"
    exit 1
    ;;
esac

echo "Database deployment completed successfully" 