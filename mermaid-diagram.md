graph TD
    A[SoloCircuit API] --> B[Root Endpoints]
    A --> C[Document Management]
    A --> D[Query API]
    A --> E[Lawsuit Data API]
    A --> F[Documentation]
    
    B --> B1[GET /]
    B --> B2[GET /health]
    
    C --> C1[GET /api/documents/count]
    C --> C2[POST /api/documents/upload]
    C --> C3[GET /api/documents/search]
    C --> C4[DELETE /api/documents/{document_id}]
    
    D --> D1[POST /api/query]
    
    E --> E1[POST /api/extract-lawsuit-data]
    
    F --> F1[GET /docs]
    F --> F2[GET /redoc]
    F --> F3[GET /openapi.json]
    
    %% Authentication note
    classDef authRequired fill:#f9d6d6,stroke:#333,stroke-width:1px
    classDef noAuth fill:#d6f9d6,stroke:#333,stroke-width:1px
    
    class B1,C1,C2,C3,C4,D1,E1 authRequired
    class B2,F1,F2,F3 noAuth
    
    %% Legend
    subgraph Legend
        L1[Requires JWT Auth]:::authRequired
        L2[No Auth Required]:::noAuth
    end