[pytest]
asyncio_default_fixture_loop_scope = function
markers =
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    auth: marks tests related to authentication

filterwarnings =
    ignore::DeprecationWarning:httpx.*: 
    ignore::DeprecationWarning:google.protobuf.*: 
    ignore::DeprecationWarning:.*uses PyType_Spec with a metaclass that has custom tp_new.*:
    ignore::DeprecationWarning:importlib.*:
    ignore:Support for class-based.*:DeprecationWarning:pydantic.*:
    ignore:Valid config keys have changed.*:UserWarning:pydantic.*:
    ignore:The class.*was deprecated.*:DeprecationWarning:langchain.*:
    ignore:Accessing the 'model_fields'.*:DeprecationWarning:chromadb.*: