#!/usr/bin/env python
"""
Generate JWT tokens for testing and development.

Usage:
    python scripts/generate_token.py

The JWT_SECRET_KEY must be set in the environment or .env file.
"""

import os
import sys
from datetime import datetime, timedelta, timezone

import jwt
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get JWT configuration from environment
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

if not JWT_SECRET_KEY:
    print("Error: JWT_SECRET_KEY environment variable must be set.")
    print("Make sure it's defined in your .env file or environment.")
    sys.exit(1)


def generate_token(expiry_days=30):
    """Generate a JWT token for API authentication."""
    # Set the token expiration time
    expiry = datetime.now(timezone.utc) + timedelta(days=expiry_days)

    # Create a payload with minimal claims
    payload = {
        "sub": "api-access",  # Subject
        "iat": datetime.now(timezone.utc),  # Issued at
        "exp": expiry,  # Expiration time
    }

    # Encode the token
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

    return token, expiry


if __name__ == "__main__":
    token, expiry = generate_token()

    print("\n=== JWT Token for SoloCircuit API ===")
    print(f"\nToken: {token}")
    print(f"\nExpires: {expiry.strftime('%Y-%m-%d %H:%M:%S')} UTC")
    print("\nUse this token in the Authorization header:")
    print(f"\nAuthorization: Bearer {token}")
    print("\n===================================\n")
