#!/bin/bash
# Development server script with authentication disabled

# Display banner
echo "===================================="
echo "    SoloCircuit API Dev Server"
echo "===================================="
echo "Starting development server with JWT authentication DISABLED"
echo "WARNING: This mode should only be used for local development!"
echo ""

# Set environment variables for development
export SKIP_AUTH=true

# Start the server with hot reload
uvicorn app.main:app --reload

# Note: The SKIP_AUTH environment variable only applies to this script execution
# and won't affect your system environment variables. 