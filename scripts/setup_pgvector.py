#!/usr/bin/env python
import os
import sys
import psycopg2
from dotenv import load_dotenv

load_dotenv()

# Get the DATABASE_URL from Heroku environment
db_url = os.getenv("DATABASE_URL")

if not db_url:
    print("DATABASE_URL environment variable not found.")
    sys.exit(1)

# Heroku uses postgres:// but psycopg2 requires postgresql://
if db_url.startswith("postgres://"):
    db_url = db_url.replace("postgres://", "postgresql://", 1)

# Create a connection
try:
    conn = psycopg2.connect(db_url)
    conn.autocommit = True
    cursor = conn.cursor()

    # Check if pgvector extension already exists
    cursor.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector';")
    extension_exists = cursor.fetchone()

    if not extension_exists:
        print("Creating pgvector extension...")
        cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        print("pgvector extension created successfully.")
    else:
        print("pgvector extension already exists.")

    # Create collection with the appropriate dimension
    collection_name = os.getenv("PGVECTOR_COLLECTION", "document_embeddings")
    dimension = 1536  # OpenAI embeddings dimension

    print(f"Setting up PGVector collection: {collection_name}")

    # Create the langchain_embedding schema if it doesn't exist
    cursor.execute("CREATE SCHEMA IF NOT EXISTS langchain_embedding;")

    # Test if the collection table exists
    cursor.execute(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'langchain_embedding'
            AND table_name = '{collection_name}'
        );
    """
    )

    result = cursor.fetchone()
    table_exists = bool(result[0]) if result else False

    if not table_exists:
        print(f"Creating collection table {collection_name}...")

        # Create the table with the appropriate structure for langchain-postgres
        cursor.execute(
            f"""
            CREATE TABLE langchain_embedding.{collection_name} (
                uuid UUID PRIMARY KEY,
                embedding vector({dimension}),
                document TEXT,
                metadata JSONB,
                cmetadata JSONB
            );
        """
        )

        # Create an index for faster vector operations
        print("Creating vector index...")
        cursor.execute(
            f"""
            CREATE INDEX ON langchain_embedding.{collection_name}
            USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100);
        """
        )

        print(f"Collection {collection_name} created successfully.")
    else:
        print(f"Collection {collection_name} already exists.")

    cursor.close()
    conn.close()
    print("Database setup completed successfully.")

except Exception as e:
    print(f"Error setting up pgvector: {e}")
    sys.exit(1)
