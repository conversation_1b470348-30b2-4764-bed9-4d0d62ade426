#!/usr/bin/env python
import time
from typing import Dict, Any, List

from dotenv import load_dotenv
from langchain.schema import Document
from tqdm import tqdm

from app.services.vector_store import VectorStoreService
from app.services.postgres_vector_store import PostgresVectorStoreService

load_dotenv()


def migrate_chroma_to_postgres():
    """
    Migrate all documents from Chroma to PostgreSQL.
    """
    print("Starting migration from Chroma to PostgreSQL vector store...")

    # Initialize vector stores
    chroma_store = VectorStoreService()
    postgres_store = PostgresVectorStoreService()

    # Get all unique user_ids from Chroma
    print("Fetching data from Chroma...")
    all_data = chroma_store.vector_store.get()

    if not all_data or not all_data.get("ids"):
        print("No data found in Chroma vector store.")
        return

    # Get all metadatas
    metadatas = all_data.get("metadatas", [])
    documents = all_data.get("documents", [])
    ids = all_data.get("ids", [])

    # Extract unique user_ids from metadata
    user_ids = set()
    user_id_to_docs: Dict[str, List[Dict[str, Any]]] = {}

    print("Processing documents and organizing by user_id...")
    for i, metadata in enumerate(metadatas):
        user_id = metadata.get("user_id")
        if not user_id:
            print(f"Warning: Document with id {ids[i]} has no user_id, skipping")
            continue

        user_ids.add(user_id)

        if user_id not in user_id_to_docs:
            user_id_to_docs[user_id] = []

        user_id_to_docs[user_id].append({"id": ids[i], "document": documents[i], "metadata": metadata})

    print(f"Found {len(user_ids)} unique users with {len(ids)} documents")

    # Migrate data user by user
    migrated_count = 0
    error_count = 0

    for user_id in tqdm(user_ids, desc="Migrating users"):
        user_docs = user_id_to_docs.get(user_id, [])
        print(f"\nMigrating {len(user_docs)} documents for user_id: {user_id}")

        # Group documents by document_id
        document_groups: Dict[str, List[Dict[str, Any]]] = {}

        for doc in user_docs:
            document_id = doc["metadata"].get("document_id")
            if not document_id:
                print(f"Warning: Document with id {doc['id']} has no document_id, using id as document_id")
                document_id = doc["id"]

            if document_id not in document_groups:
                document_groups[document_id] = []

            document_groups[document_id].append(doc)

        # Process each document group
        for document_id, doc_group in document_groups.items():
            try:
                # Convert to LangChain documents
                lc_docs = []

                # Use the first document's metadata for common metadata
                common_metadata = {}
                if doc_group and doc_group[0]["metadata"]:
                    # Filter out document_id and user_id from common metadata
                    common_metadata = {
                        k: v for k, v in doc_group[0]["metadata"].items() if k not in ["document_id", "user_id"]
                    }

                for doc in doc_group:
                    lc_docs.append(Document(page_content=doc["document"], metadata=doc["metadata"]))

                # Add to PostgreSQL
                postgres_store.add_documents(lc_docs, user_id, common_metadata)
                migrated_count += len(lc_docs)

                # Wait briefly to avoid overloading the database
                time.sleep(0.1)

            except Exception as e:
                print(f"Error migrating document_id {document_id}: {str(e)}")
                error_count += 1

    # Print summary
    print("\nMigration completed!")
    print(f"Successfully migrated {migrated_count} documents")
    if error_count > 0:
        print(f"Encountered {error_count} errors during migration")

    # Verify the migration
    verify_migration(chroma_store, postgres_store)


def verify_migration(chroma_store: VectorStoreService, postgres_store: PostgresVectorStoreService):
    """Verify that the migration was successful by comparing document counts."""
    try:
        # Get counts from both stores without user_id filter
        chroma_count = chroma_store.get_document_count()
        postgres_count = postgres_store.get_document_count()

        print("\nVerification Results:")
        print(f"Chroma document count: {chroma_count}")
        print(f"PostgreSQL document count: {postgres_count}")

        if postgres_count >= chroma_count:
            print("✅ Migration verification passed!")
        else:
            print("⚠️ Migration verification failed - document counts don't match")
    except Exception as e:
        print(f"Error during verification: {str(e)}")


if __name__ == "__main__":
    migrate_chroma_to_postgres()
