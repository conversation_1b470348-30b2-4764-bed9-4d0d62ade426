#!/usr/bin/env python3
"""
<PERSON>ript to generate secure random keys for Heroku config.
Run this script to generate a JWT secret key.
"""
import secrets
import argparse
import base64
import sys


def generate_hex_key(length=64):
    """Generate a secure random hex string of the specified length."""
    return secrets.token_hex(length // 2)


def generate_base64_key(length=64):
    """Generate a secure random base64 string of approximately the specified length."""
    # Generate random bytes
    random_bytes = secrets.token_bytes(length)
    # Convert to base64
    token = base64.urlsafe_b64encode(random_bytes).decode("utf-8").rstrip("=")
    return token


def set_heroku_config(app_name, key_name, key_value):
    """Set a config var in Heroku."""
    import subprocess

    cmd = f"heroku config:set {key_name}={key_value} --app {app_name}"
    print(f"Running: {cmd}")

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            check=True,
            text=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        print(result.stdout)
        print(f"Successfully set {key_name} for {app_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error setting {key_name}: {e.stderr}", file=sys.stderr)
        return False


def main():
    parser = argparse.ArgumentParser(description="Generate secure keys for Heroku apps")
    parser.add_argument("--app", help="Heroku app name to set the key for (optional)")
    parser.add_argument(
        "--key-name",
        default="JWT_SECRET_KEY",
        help="Name of the config var (default: JWT_SECRET_KEY)",
    )
    parser.add_argument("--apply", action="store_true", help="Apply the generated key to Heroku app")
    parser.add_argument(
        "--format",
        choices=["hex", "base64"],
        default="hex",
        help="Format of the key (hex or base64)",
    )
    parser.add_argument("--length", type=int, default=64, help="Length of the key in characters")

    args = parser.parse_args()

    # Generate the key based on the format
    if args.format == "hex":
        key = generate_hex_key(args.length)
    else:  # base64
        key = generate_base64_key(args.length)

    # Display the generated key
    print(f"Generated {args.format} key ({len(key)} chars):")
    print(key)

    # If --apply is set and --app is provided, set the key in Heroku
    if args.apply:
        if not args.app:
            print("Error: --app is required when using --apply", file=sys.stderr)
            sys.exit(1)

        set_heroku_config(args.app, args.key_name, key)

    return key


if __name__ == "__main__":
    main()
