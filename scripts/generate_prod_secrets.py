#!/usr/bin/env python3
"""
Generate a secure JWT secret key and a JWT token for production.

Usage:
    python scripts/generate_prod_secrets.py

No environment variables required - generates both the key and token.
"""

import secrets
from datetime import datetime, timedelta, timezone
import jwt


def generate_secret_key(length=64):
    """Generate a secure random hex string for use as a JWT secret key."""
    return secrets.token_hex(length // 2)


def generate_token(secret_key, expiry_days=30, algorithm="HS256"):
    """Generate a JWT token using the provided secret key."""
    # Set the token expiration time
    expiry = datetime.now(timezone.utc) + timedelta(days=expiry_days)

    # Create a payload with minimal claims
    payload = {
        "sub": "api-access",  # Subject
        "iat": datetime.now(timezone.utc),  # Issued at
        "exp": expiry,  # Expiration time
    }

    # Encode the token
    token = jwt.encode(payload, secret_key, algorithm=algorithm)

    return token, expiry


if __name__ == "__main__":
    # Generate a secure secret key
    secret_key = generate_secret_key()

    # Generate a JWT token using this key
    token, expiry = generate_token(secret_key)

    print("\n=== Production Credentials ===")
    print(f"\nJWT_SECRET_KEY: {secret_key}")
    print(f"\nJWT_ALGORITHM: HS256")
    print(f"\nSample JWT Token: {token}")
    print(f"\nToken Expires: {expiry.strftime('%Y-%m-%d %H:%M:%S')} UTC")
    print("\nUse this token in the Authorization header:")
    print(f"\nAuthorization: Bearer {token}")
    print("\nTo set in Heroku:")
    print(f"\nheroku config:set JWT_SECRET_KEY={secret_key} --app YOUR_APP_NAME")
    print("\n===================================\n")
