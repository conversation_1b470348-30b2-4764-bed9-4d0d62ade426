[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "solo_circuit"
version = "0.1.0"
description = "A FastAPI-based web service for document processing, semantic search, and structured data extraction"
authors = ["SoloSuit Inc. <<EMAIL>>"]
readme = "README.md"
license = "MIT"
package-mode = false

[tool.black]
line-length = 120
target-version = ['py312']
skip-string-normalization = false

[tool.poetry.dependencies]
python = ">=3.9,<3.12"
fastapi = "^0.109.2"
uvicorn = "^0.27.1"
python-multipart = "^0.0.9"
langchain = ">=0.1.17"
langchain-community = ">=0.0.36"
langchain-openai = ">=0.0.7"
langchain-anthropic = ">=0.1.4"
langchain-google-genai = ">=0.0.11"
google-generativeai = ">=0.4.1"
openai = ">=1.14.0"
anthropic = ">=0.20.0"
pypdf = ">=4.1.0"
pytesseract = ">=0.3.10"
pillow = ">=10.2.0"
python-docx = ">=1.1.0"
faiss-cpu = ">=1.7.4"
chromadb = ">=0.4.22"
pydantic = ">=2.6.3"
email-validator = ">=2.1.0"
python-dotenv = ">=1.0.0"
types-requests = ">=********"
python-jose = ">=3.4.0"
pyjwt = ">=2.8.0"
langchain-postgres = ">=0.0.1"
psycopg2-binary = ">=2.9.9"
sqlalchemy = ">=2.0.27"
pgvector = ">=0.2.4"
gunicorn = ">=21.2.0"
starlette = "^0.36.3"
httpx = "^0.27.0"
onnxruntime = ">=1.16.2,<1.20.0"
typing-extensions = ">=4.9.0"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
flake8 = "^7.0.0"
pytest = "^8.0.0"
pytest-cov = "^6.1.1"
mypy = "^1.8.0"
types-PyJWT = "^1.7.1"
types-python-jose = "^3.3.0"

[tool.pytest]
testpaths = ["tests"]
pythonpath = ["."]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--xvs",  # Show extra test summary info
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=xml:coverage.xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests that require external services",
    "api: marks API tests",
]

[tool.coverage.run]
omit = [
    "tests/*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true

[[tool.mypy.overrides]]
module = [
    "pydantic.*",
    "fastapi.*",
    "langchain.*",
    "langchain_community.*",
    "langchain_openai.*",
    "langchain_anthropic.*",
    "langchain_google_genai.*",
    "langchain_core.*",
    "dotenv.*",
    "google.*",
    "jwt.*",
    "starlette.*",
    "langchain_postgres.*",
    "pgvector.*"
]
ignore_missing_imports = true 