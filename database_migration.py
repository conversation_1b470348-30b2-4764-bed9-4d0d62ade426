#!/usr/bin/env python
import os
import sys
import asyncio
import logging
import re
import argparse
from dotenv import load_dotenv

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.ext.asyncio import create_async_engine
import asyncpg

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Parse command line arguments
parser = argparse.ArgumentParser(description="Database migration script")
parser.add_argument("--create-db", action="store_true", help="Create database if it doesn't exist")
parser.add_argument("--force", action="store_true", help="Force database operations")
args = parser.parse_args()

# Load environment variables
load_dotenv()

# Determine environment
ENV = os.getenv("ENVIRONMENT", "development").lower()
logger.info(f"Running in {ENV} environment")

# Get PostgreSQL URL from environment
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    logger.error("Error: DATABASE_URL environment variable is not set")
    sys.exit(1)

# Handle Heroku-style postgres:// URLs by converting to postgresql://
if DATABASE_URL.startswith("postgres://"):
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)
    logger.info("Converted Heroku postgres:// URL to postgresql:// format")

# Ensure the URL has the asyncpg driver for SQLAlchemy
if "+asyncpg" not in DATABASE_URL and "postgresql://" in DATABASE_URL:
    DATABASE_URL = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://", 1)
    logger.info("Added asyncpg driver to PostgreSQL URL for SQLAlchemy compatibility")

# Extract connection details from the PostgreSQL URL
url_pattern = r"postgresql(?:\+\w+)?:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)"
match = re.match(url_pattern, DATABASE_URL)

if match:
    user, password, host, port, original_db = match.groups()

    # On Heroku (staging/production), use the provided database
    # In local development, use our custom database names
    is_heroku = "HEROKU_APP_NAME" in os.environ or ENV in ["staging", "production"]

    if is_heroku:
        logger.info(f"Running on Heroku, using provided database: {original_db}")
        DB_NAME = original_db
    else:
        # For local development/testing
        if ENV == "production":
            DB_NAME = "solocircuit_prod"
        elif ENV == "staging":
            DB_NAME = "solocircuit_staging"
        else:  # development or test
            DB_NAME = "solocircuit_test"
        logger.info(f"Using database: {DB_NAME}")

    # Create a URL that points to the default postgres database for admin operations
    admin_url = f"postgresql+asyncpg://{user}:{password}@{host}:{port}/postgres"

    # Create a URL for the target database (for migrations)
    target_url = f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{DB_NAME}"

    # Connection string for asyncpg (used for database creation)
    conn_string = f"postgres://{user}:{password}@{host}:{port}/postgres"
else:
    logger.error(f"Could not parse PostgreSQL URL: {DATABASE_URL}")
    sys.exit(1)


async def database_exists(conn_string, db_name):
    """Check if the database exists."""
    try:
        conn = await asyncpg.connect(conn_string)
        result = await conn.fetch("SELECT 1 FROM pg_database WHERE datname = $1", db_name)
        await conn.close()
        return bool(result)
    except Exception as e:
        logger.error(f"Error checking if database exists: {e}")
        return False


async def create_database(conn_string, db_name):
    """Create the database if it doesn't exist."""
    try:
        # Connect to the default postgres database
        conn = await asyncpg.connect(conn_string)

        # Check if the database already exists
        exists = await database_exists(conn_string, db_name)

        if exists:
            logger.info(f"Database {db_name} already exists.")
            await conn.close()
            return True

        # Create the database
        await conn.execute(f'CREATE DATABASE "{db_name}"')
        logger.info(f"Created database {db_name}")

        # Close the connection
        await conn.close()
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


async def run_migrations():
    """Run database migrations to set up tables"""
    try:
        # Check if we're running on Heroku
        is_heroku = "HEROKU_APP_NAME" in os.environ or ENV in ["staging", "production"]

        # Only try to create the database if we're not on Heroku and requested to
        if not is_heroku and (args.create_db or args.force):
            db_created = await create_database(conn_string, DB_NAME)
            if not db_created and not args.force:
                logger.error(f"Could not create database {DB_NAME}")
                sys.exit(1)

        # Use the database URL directly if we're on Heroku
        engine_url = DATABASE_URL if is_heroku else target_url
        logger.info(f"Using connection URL: {engine_url}")

        # Create async engine
        engine = create_async_engine(engine_url, echo=True)

        # Create tables
        async with engine.begin() as conn:
            logger.info("Creating tables if they don't exist...")
            await conn.run_sync(metadata.create_all)

        logger.info("Migration completed successfully")

    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")

        # Check if the error is about the database not existing
        if ("database" in str(e).lower() and "not exist" in str(e).lower()) or ("does not exist" in str(e).lower()):
            logger.error(f"Database {DB_NAME} does not exist.")
            logger.info(f"Run again with --create-db flag to create the database.")

        sys.exit(1)


# Define metadata
metadata = sa.MetaData()

# Define llm_traces table
llm_traces = sa.Table(
    "llm_traces",
    metadata,
    sa.Column("id", UUID, primary_key=True),
    sa.Column("user_id", sa.String, nullable=True, index=True),
    sa.Column("session_id", sa.String, nullable=True, index=True),
    sa.Column("request_id", sa.String, nullable=True, index=True),
    sa.Column("provider", sa.String, nullable=False, index=True),
    sa.Column("model", sa.String, nullable=False, index=True),
    sa.Column("prompt", JSONB, nullable=False),
    sa.Column("prompt_tokens", sa.Integer, nullable=True),
    sa.Column("response", JSONB, nullable=True),
    sa.Column("completion_tokens", sa.Integer, nullable=True),
    sa.Column("total_tokens", sa.Integer, nullable=True),
    sa.Column("status", sa.String, nullable=False, index=True),
    sa.Column("error_message", sa.Text, nullable=True),
    sa.Column("duration_ms", sa.Integer, nullable=True),
    sa.Column("parameters", JSONB, nullable=True),
    sa.Column("metadata", JSONB, nullable=True),
    sa.Column("tags", sa.ARRAY(sa.String), nullable=True, index=True),
    sa.Column("created_at", sa.DateTime, default=sa.func.now(), nullable=False, index=True),
    sa.Column("updated_at", sa.DateTime, nullable=True, onupdate=sa.func.now()),
)

if __name__ == "__main__":
    logger.info("Starting database migration")
    asyncio.run(run_migrations())
    logger.info("Database migration complete")
