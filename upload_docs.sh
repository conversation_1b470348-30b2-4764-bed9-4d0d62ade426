#!/bin/bash

TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0X3VzZXJfMiIsImlhdCI6MTc0NTU0Mzg5NSwiZXhwIjoxNzc3MDc5ODk1fQ.AqDw8HgTl0crh_93zY_o43xSJDh-KJDYx_-1f2vapNc"
URL="https://solocircuit-api-staging-2b0599febf30.herokuapp.com/api/documents"
USER_ID="test_user_2"

# Upload only the settlement agreement file
FILE="test_docs/test_user_2/upload_settlement_agreement-928579.pdf"
echo "Uploading $FILE..."
echo "File size: $(ls -lh "$FILE" | awk '{print $5}')"
curl -v -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -F "file=@$FILE" \
    "$URL?user_id=$USER_ID"
echo -e "\n" 