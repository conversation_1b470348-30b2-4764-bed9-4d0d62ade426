#!/usr/bin/env python
import sys
import subprocess
import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Parse command line arguments
parser = argparse.ArgumentParser(description="Drop unused tables in Heroku")
parser.add_argument("--app", required=True, help="Heroku app name (e.g., solocircuit-api-staging)")
parser.add_argument(
    "--dry-run",
    action="store_true",
    help="Show what would be done without making changes",
)
args = parser.parse_args()

# Tables to drop
TABLES_TO_DROP = ["prompt_details", "response_details"]


def run_heroku_command(command):
    """Run a command against Heroku and return the output."""
    full_command = f'heroku pg:psql -a {args.app} -c "{command}"'
    logger.info(f"Running: {full_command}")

    try:
        result = subprocess.run(full_command, shell=True, check=True, text=True, capture_output=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"Output: {e.stdout}")
        logger.error(f"Error: {e.stderr}")
        sys.exit(1)


def check_table_exists(table_name):
    """Check if a table exists in the Heroku database."""
    query = f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name='{table_name}');"
    output = run_heroku_command(query)

    # Parse the output to determine if the table exists
    return "t" in output.lower() and "f" not in output.lower()


def drop_table(table_name):
    """Drop a table from the Heroku database."""
    if args.dry_run:
        logger.info(f"Would drop table '{table_name}' (dry run)")
        return

    query = f'DROP TABLE "{table_name}" CASCADE;'
    output = run_heroku_command(query)
    logger.info(f"Dropped table '{table_name}'")
    logger.info(f"Output: {output}")


def main():
    """Main function to drop unused tables."""
    logger.info(f"Checking tables in Heroku app: {args.app}")

    for table in TABLES_TO_DROP:
        exists = check_table_exists(table)

        if exists:
            logger.info(f"Table '{table}' exists in {args.app}")
            drop_table(table)
        else:
            logger.info(f"Table '{table}' does not exist in {args.app}")

    if args.dry_run:
        logger.info("Dry run completed. No changes were made.")
    else:
        logger.info("Tables cleanup completed.")


if __name__ == "__main__":
    main()
