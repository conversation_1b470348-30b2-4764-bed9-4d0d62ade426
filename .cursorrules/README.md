# Python Developer .cursorrules prompt file

Author: <PERSON>

## What you can build
Command-Line Automation Suite: Develop a command-line tool that automates repetitive tasks such as file management, text processing, and data transformation using Python and rich CLI interfaces powered by click and prompt-toolkit.Interactive Data Validation Tool: Create a command-line application that validates and processes data files (e.g., CSV, JSON) using pydantic for type checking, offering rich text feedback using rich and progress tracking with tqdm.Template-Based Report Generator: Design a tool for generating customizable reports from templates using jinja2, allowing users to specify parameters through an interactive command-line interface.Clipboard Manager with Text Formatting: Build a clipboard manager that supports rich text formatting and unicode operations using pyperclip and colorama, suitable for developers and writers who frequently copy and paste code snippets.Interactive File System Navigator: Implement a command-line tool for exploring and manipulating file systems with an intuitive interface, personalized shortcuts, and enhanced visualization using tabulate for directory listings.Real-Time Command-Line Chat Interface: Develop a real-time messaging application for the command line that uses prompt-toolkit for interactions and colorama for colored messages, suitable for developer collaboration in server environments.Code Snippet Tokenization Utility: Create a utility that tokenizes and analyzes code snippets or text inputs using tiktoken, providing insights and statistics through a user-friendly command-line interface.Progressive Learning CLI Quizzes: Design a command-line quiz application that quizzes users on Python and software development topics, utilizing click for user interactions and tqdm for progress visualization.Poetry-Based Dependency Manager with UI: Enhance the poetry dependency manager by integrating a user interface for managing Python project dependencies through the terminal, simplifying package version updates and installation tasks.Pyperclip Enhanced Copy-Paste Tool: Build a tool that extends clipboard functionalities, offering options for auto-formatting and macro operations directly from the command line.

## Benefits


## Synopsis
Developers working on Python command-line tools and file operations can create modular, efficient, and well-documented CLI applications with robust text formatting, templating, and data validation features.

## Overview of .cursorrules prompt
The .cursorrules file outlines the responsibilities and attributes of an elite software developer skilled in Python, command-line tools, and file system operations. It emphasizes a pragmatic approach to coding, modular design, principled coding practices like KISS and DRY, and the importance of documentation and testing. The developer is expected to have a functional programming preference and work with a technological stack that includes Python 3.6+, alongside various dependencies such as `rich`, `click`, `jinja2`, `prompt-toolkit`, and others for building robust command-line applications and ensuring code quality through testing and validation.

