# Unit Testing the PostgresVectorStoreService

This document provides examples and best practices for unit testing the `PostgresVectorStoreService` class.

## Overview

Unit tests for the `PostgresVectorStoreService` focus on testing the service's methods in isolation by mocking its dependencies. The primary dependency that we mock is the `PGVector` class from LangChain.

## Test Structure

Tests are organized in the `TestPostgresVectorStoreService` class within the `tests/unit/services/test_postgres_vector_store.py` file.

Each test method focuses on testing a specific functionality of the service:

1. Initialization tests
2. Document addition tests
3. Document search tests
4. Document count tests
5. Document deletion tests
6. Document clearing tests

## Fixtures

We use pytest fixtures to set up common test dependencies:

```python
@pytest.fixture
def mock_pgvector():
    """Mock the PGVector class."""
    # Create instance mock first
    pgvector_instance = Mock()
    
    # Create class mock that returns the instance
    mock_pgvector_class = Mock()
    mock_pgvector_class.return_value = pgvector_instance
    
    # Patch the class
    with patch("app.services.postgres_vector_store.PGVector", mock_pgvector_class):
        yield pgvector_instance

@pytest.fixture
def mock_embedding_model():
    """Mock an embedding model."""
    return MagicMock()
```

## Testing the `get_document_count` Method

The `get_document_count` method has two main scenarios to test:

1. Getting the count for a specific user
2. Getting the total count across all users

Here's how we test these scenarios:

```python
def test_get_document_count_with_user_id(self, mock_pgvector, mock_embedding_model):
    """Test getting document count for a specific user."""
    # Setup mock for similarity_search_with_score
    mock_results = Mock()
    mock_results.count = 5
    mock_pgvector.similarity_search_with_score.return_value = mock_results

    # Create service and get count
    service = PostgresVectorStoreService(mock_embedding_model)
    count = service.get_document_count(user_id="test_user")

    # Verify correct filter was used and count is returned
    mock_pgvector.similarity_search_with_score.assert_called_once_with(
        "", k=1, filter={"user_id": "test_user"}
    )
    assert count == 5

def test_get_document_count_without_user_id(self, mock_pgvector, mock_embedding_model):
    """Test getting total document count across all users."""
    # Setup mock for similarity_search_with_score
    mock_results = Mock()
    mock_results.count = 10
    mock_pgvector.similarity_search_with_score.return_value = mock_results

    # Create service and get count
    service = PostgresVectorStoreService(mock_embedding_model)
    count = service.get_document_count()

    # Verify no filter was used and count is returned
    mock_pgvector.similarity_search_with_score.assert_called_once_with(
        "", k=1
    )
    assert count == 10
```

## Test Execution

Run the tests using pytest:

```bash
# Run all tests
pytest tests/unit/services/test_postgres_vector_store.py -v

# Run specific test
pytest tests/unit/services/test_postgres_vector_store.py::TestPostgresVectorStoreService::test_get_document_count_with_user_id -v
```

## Best Practices

1. **Isolate dependencies**: Mock all external dependencies to ensure unit tests only test the service's logic.
2. **Test error cases**: Include tests for edge cases and error conditions.
3. **Verify method calls**: Check that the service is calling the underlying PGVector methods with the correct parameters.
4. **Keep tests focused**: Each test should verify a specific aspect of the service's behavior.
5. **Use descriptive test names**: Test names should clearly indicate what functionality is being tested. 