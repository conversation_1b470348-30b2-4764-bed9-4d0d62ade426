# PostgreSQL Vector Store Service

The `PostgresVectorStoreService` provides a high-level interface for storing, retrieving, and managing document embeddings in a PostgreSQL database using the pgvector extension.

## Overview

This service encapsulates all interactions with the vector database, providing methods for common operations:

- Adding documents to the vector store
- Searching for documents based on semantic similarity
- Deleting documents by ID
- Counting documents in the database
- Clearing documents by user or all documents

## Implementation Details

The service uses LangChain's PGVector integration to handle the low-level operations with PostgreSQL. Document embeddings are generated using OpenAI's embedding model by default, but the service supports any LangChain-compatible embedding model.

## Configuration

The service is configured through environment variables:

- `DATABASE_URL`: PostgreSQL connection string (defaults to `postgresql://postgres:postgres@localhost:5432/vectordb`)
- `PGVECTOR_COLLECTION`: Collection name for storing embeddings (defaults to `document_embeddings`)

## Usage Examples

### Initializing the Service

```python
from langchain_openai import OpenAIEmbeddings
from app.services.postgres_vector_store import PostgresVectorStoreService

# Initialize with default OpenAI embeddings
vector_store = PostgresVectorStoreService()

# Or initialize with a custom embedding model
custom_embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
vector_store = PostgresVectorStoreService(embedding_model=custom_embeddings)
```

### Adding Documents

```python
from langchain.schema import Document

# Create document objects
documents = [
    Document(page_content="This is a sample document", metadata={"source": "example.txt"}),
    Document(page_content="Another sample document", metadata={"source": "example2.txt"})
]

# Add to vector store with user ID and additional metadata
user_id = "user123"
additional_metadata = {"category": "samples", "importance": "high"}
document_id = vector_store.add_documents(documents, user_id, additional_metadata)

print(f"Added documents with ID: {document_id}")
```

### Searching Documents

```python
# Search for documents relevant to a query
results = vector_store.search_documents(
    query="Find similar documents to this text",
    user_id="user123",
    k=5  # Number of results to return
)

# Process search results
for doc in results:
    print(f"Content: {doc.page_content}")
    print(f"Metadata: {doc.metadata}")
    print("---")
```

### Document Count

```python
# Get total document count for a specific user
user_count = vector_store.get_document_count(user_id="user123")
print(f"User has {user_count} documents")

# Get total document count across all users
total_count = vector_store.get_document_count()
print(f"Total documents: {total_count}")
```

### Deleting Documents

```python
# Delete a specific document for a user
vector_store.delete_document(document_id="doc123", user_id="user456")

# Clear all documents for a user
vector_store.clear(user_id="user123")

# Clear all documents in the database
vector_store.clear()
```

## Testing

The service includes comprehensive unit tests that verify its functionality using mocks for the underlying PGVector class. See `tests/unit/services/test_postgres_vector_store.py` for test examples.

## Error Handling

The service includes basic error handling for common issues, such as:

- Empty document lists
- Database connection errors
- Filter validation

For production use, additional error handling and retry logic may be necessary. 