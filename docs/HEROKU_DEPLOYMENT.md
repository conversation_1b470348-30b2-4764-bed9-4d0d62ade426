# Deploying SoloCircuit on Heroku

This guide walks through the process of deploying the SoloCircuit API on Heroku with PostgreSQL and pgvector.

## Prerequisites

1. A Heroku account ([sign up here](https://signup.heroku.com/))
2. Heroku CLI installed ([installation instructions](https://devcenter.heroku.com/articles/heroku-cli))
3. Git installed and repository cloned

## Step 1: Prepare Your Repository

Ensure your repository has the necessary Heroku configuration files:

- `Procfile`: Tells Heroku how to run your application
- `runtime.txt`: Specifies the Python version
- `app.json`: Describes your application for Heroku
- `requirements.txt`: Lists all dependencies

These should already be included in the repository.

## Step 2: Create a Heroku Application

```bash
# Login to Heroku
heroku login

# Create a new Heroku application
heroku create solocircuit-api  # Replace with your preferred app name

# Add the heroku remote to your git repository
git remote add heroku https://git.heroku.com/solocircuit-api.git
```

## Step 3: Provision a PostgreSQL Database

You'll need a Standard tier Postgres database to support pgvector:

```bash
# Add PostgreSQL database (standard tier required for pgvector)
heroku addons:create heroku-postgresql:standard-0
```

## Step 4: Configure Environment Variables

Set all required environment variables:

```bash
# Set LLM API keys
heroku config:set OPENAI_API_KEY=your-openai-api-key
heroku config:set ANTHROPIC_API_KEY=your-anthropic-api-key

# Set JWT authentication settings
heroku config:set JWT_SECRET_KEY=your-secure-jwt-secret
heroku config:set JWT_ALGORITHM=HS256

# Set vector store type to postgres
heroku config:set VECTOR_STORE_TYPE=postgres
heroku config:set PGVECTOR_COLLECTION=document_embeddings

# Set Python environment
heroku config:set PYTHONUNBUFFERED=True
```

## Step 5: Deploy the Application

```bash
# Deploy to Heroku
git push heroku main
```

The application will be deployed, and the `setup_pgvector.py` script will automatically run as part of the post-deploy process to set up the pgvector extension.

## Step 6: Verify the Installation

1. Check that the application is running:

```bash
# Open the app in your browser
heroku open

# Check the application logs
heroku logs --tail
```

2. Verify the pgvector extension is installed:

```bash
# Connect to the PostgreSQL database
heroku pg:psql

# List installed extensions
\dx

# Should show 'vector' in the list
# Exit psql
\q
```

## Step 7: Migrating Data (Optional)

If you have existing documents in Chroma that you want to migrate to PostgreSQL:

1. Configure local environment with Heroku database URL:

```bash
# Get the DATABASE_URL
heroku config:get DATABASE_URL

# Add it to your local .env file along with other required variables
# VECTOR_STORE_TYPE=postgres
# DATABASE_URL=postgresql://...
```

2. Run the migration script:

```bash
python scripts/migrate_chroma_to_postgres.py
```

## Scaling the Application

To scale the application:

```bash
# Scale web dynos up or down
heroku ps:scale web=2

# Upgrade database if needed
heroku addons:upgrade heroku-postgresql:premium-0
```

## Monitoring and Maintenance

- **Logs**: `heroku logs --tail`
- **Database Status**: `heroku pg:info`
- **Performance Metrics**: Available in the Heroku Dashboard
- **Database Backups**: `heroku pg:backups:capture`

## Troubleshooting

### Application Crashes

Check the logs to identify issues:

```bash
heroku logs --tail
```

### Database Issues

If the pgvector extension wasn't installed correctly:

```bash
# Run the setup script manually
heroku run python scripts/setup_pgvector.py
```

### Connection Errors

If the application can't connect to the database:

1. Verify the DATABASE_URL is set correctly
2. Check if the database is up: `heroku pg:info`
3. Restart the application: `heroku restart`

## Additional Resources

- [Heroku Python Support](https://devcenter.heroku.com/articles/python-support)
- [Heroku Postgres Documentation](https://devcenter.heroku.com/articles/heroku-postgresql)
- [pgvector Documentation](https://github.com/pgvector/pgvector) 