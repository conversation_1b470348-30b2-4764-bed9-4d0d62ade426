# SoloCircuit Documentation

Welcome to the SoloCircuit documentation. This project provides a FastAPI-based API for document processing, embedding, and retrieval using LangChain and PostgreSQL with pgvector.

## Application Documentation

- [PostgreSQL Vector Store Service](postgres_vector_store.md) - Documentation for our core vector store service
- [JWT Authentication](JWT_AUTHENTICATION.md) - Guide to setting up JWT authentication
- [Unit Testing](unit_testing.md) - Guidelines and examples for unit testing the vector store service

## Package Documentation

- [FastAPI](packages/fastapi.md) - Web framework for building APIs
- [LangChain](packages/langchain.md) - Framework for developing LLM applications
- [LangChain Community](packages/langchain_community.md) - Community integrations for LangChain
- [pgvector](packages/pgvector.md) - PostgreSQL extension for vector similarity search
- [CrewAI](packages/crewai.md) - Framework for orchestrating AI agents

## API Endpoints

- `/api/documents/count` - Get document count for a user
- `/api/documents/upload` - Upload documents to the vector store
- `/api/documents/search` - Search for documents by semantic similarity
- `/api/documents/{document_id}` - Delete a specific document

## Development

- Tests are located in the `tests/` directory
- Main application code is in the `app/` directory
- Environment variables are loaded from `.env` file

## Getting Started

1. Install PostgreSQL and the pgvector extension
2. Set up your environment variables 
3. Install dependencies with `pip install -r requirements.txt`
4. Run the application with `uvicorn app.main:app --reload`

For more detailed setup instructions, see the project README. 