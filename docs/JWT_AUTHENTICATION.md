# JWT Authentication Setup

This document explains how to set up JWT-based authentication for the SoloCircuit API to securely authenticate requests from your Rails 7.0 application.

## Overview

SoloCircuit uses JWT (JSON Web Token) authentication to secure all API endpoints. The authentication process is simple:

1. Your Rails application securely stores a JWT token in an environment variable
2. Your Rails application sends this token in the Authorization header with every request
3. SoloCircuit validates the token using a shared secret key

## Environment Variables

### On the SoloCircuit FastAPI Server:

Set the following environment variables:

```bash
# Required: The secret key used to validate JWT tokens
# This should be a complex, randomly generated string
JWT_SECRET_KEY=your-very-secure-secret-key-here

# Optional: The algorithm used for JWT (defaults to HS256)
JWT_ALGORITHM=HS256
```

### On Your Rails 7.0 Application:

Set the following environment variable:

```bash
# The API token for accessing the SoloCircuit API
SOLOCIRCUIT_API_TOKEN=your-jwt-token-here

# The URL of the SoloCircuit API
SOLOCIRCUIT_API_URL=http://localhost:8000
```

## Token Generation

The JWT token should be generated securely and set as an environment variable on both sides. Here's an example of how to generate a secure JWT token:

```ruby
require 'jwt'

# Generate a secure token with minimal payload
payload = { app_name: "rails-app" }
secret_key = "your-very-secure-secret-key-here"  # Must match JWT_SECRET_KEY on SoloCircuit

# Create the token
token = JWT.encode(payload, secret_key, 'HS256')
puts "Your JWT token: #{token}"
```

## Making Authenticated Requests

All requests to the SoloCircuit API (except `/health` and `/`) must include the JWT token in the Authorization header:

```
Authorization: Bearer your-jwt-token-here
```

## Client Library

For easy integration with your Rails application, we provide a simple client library in `examples/solocircuit_client.rb`. This library handles authentication and provides an easy-to-use interface for all SoloCircuit API endpoints.

### Installation

Add the following to your Gemfile:

```ruby
gem 'faraday', '~> 2.8.1'
gem 'faraday-multipart', '~> 1.0.4'
gem 'jwt', '~> 2.8.0'  # If you need to work with JWTs in your application
```

### Usage

```ruby
# Initialize the client
client = SoloCircuit::Client.new

# Upload a document
result = client.upload_document(
  user_id: current_user.id,
  file: File.open('path/to/file.pdf')
)

# Query documents
response = client.query_documents(
  user_id: current_user.id,
  query: "What are the key points in the document?"
)
```

See `examples/solocircuit_usage.rb` for complete usage examples.

## Security Best Practices

1. **Use Environment Variables**: Always store sensitive information like JWT tokens and secrets in environment variables, never in code.

2. **Secure Secret Keys**: Use a strong, randomly generated secret key for JWT validation.

3. **TLS/SSL**: Always use HTTPS in production to encrypt data in transit.

4. **Minimal Payload**: Keep the JWT payload minimal to reduce token size and potential exposure of sensitive data.

5. **Regular Rotation**: Consider rotating the JWT token and secret key periodically for enhanced security.

## Troubleshooting

If you encounter 401 Unauthorized errors:

1. Verify the JWT token is correctly set in your Rails environment variable
2. Check that the token is properly included in the Authorization header
3. Ensure the JWT_SECRET_KEY on the SoloCircuit server matches the secret used to generate the token
4. Verify the token hasn't expired (if expiration was set)

## Testing

You can test authentication using curl:

```bash
curl -H "Authorization: Bearer your-jwt-token-here" http://localhost:8000/api/documents/count?user_id=test_user
``` 