# FastAPI Documentation

FastAPI is a modern, fast web framework for building APIs with Python. It's based on standard Python type hints and provides automatic validation, serialization, and documentation.

## Key Features

- **Fast**: Very high performance, on par with NodeJS and Go
- **Easy to Use**: Designed to be easy to use and learn
- **Schema Validation**: Automatic data validation with Pydantic
- **OpenAPI**: Automatic interactive documentation
- **Authentication**: Support for JWT, OAuth2, and other security methods
- **Async Support**: Built on Starlette with full async/await support

## Usage in SoloCircuit

In SoloCircuit, FastAPI is used to create the API endpoints for document management, querying, and authentication. Here's an example of how we use FastAPI:

```python
from fastapi import FastAPI, Depends, HTTPException, Query
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

from app.services.postgres_vector_store import PostgresVectorStoreService

app = FastAPI(
    title="SoloCircuit",
    description="AI document processing API for SoloSuit",
    version="1.0.0",
)

# JWT authentication
security = HTTPBearer()

# Dependency for vector store
def get_vector_store():
    embedding_model = OpenAIEmbeddings()
    return PostgresVectorStoreService(embedding_model)

@app.get("/api/documents/count")
async def get_document_count(
    user_id: str = Query(...),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    vector_store: PostgresVectorStoreService = Depends(get_vector_store),
):
    # Validate JWT token (using auth middleware)
    # ...
    
    # Get document count for the user
    count = vector_store.get_document_count(user_id)
    return {"count": count}
```

## Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Starlette Documentation](https://www.starlette.io/)
- [Pydantic Documentation](https://docs.pydantic.dev/) 