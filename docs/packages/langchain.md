# LangChain Documentation

LangChain is a framework for developing applications powered by language models. It provides tools and components for working with LLMs (Large Language Models), including prompting, memory, indexes, and chains.

## Key Components

- **Chains**: Combine LLMs with other components for complex applications
- **Agents**: Autonomous agents that can use tools based on user instructions
- **Memory**: Stateful memory for conversations and history
- **Indexes**: Tools for structuring documents for efficient retrieval
- **Retrievers**: Methods to retrieve relevant context for LLM calls
- **Vector Stores**: Store and search vector embeddings for semantic search

## Usage in SoloCircuit

In SoloCircuit, LangChain is used primarily for document processing, embeddings, and vector storage. Our PostgresVectorStoreService is built on top of LangChain's vector store components:

```python
from langchain.embeddings.base import Embeddings
from langchain.schema import Document
from langchain_openai import OpenAIEmbeddings
from langchain_postgres import PGVector

class PostgresVectorStoreService:
    """Service to handle document embeddings and vector store operations using PostgreSQL."""

    def __init__(self, embedding_model: Optional[Embeddings] = None):
        # Initialize embedding model
        self.embedding_model = embedding_model or OpenAIEmbeddings()
        
        # Initialize PGVector
        self.vector_store = PGVector(
            collection_name=self.collection_name,
            connection_string=self.connection_string,
            embedding_function=self.embedding_model,
            use_existing_collection=True,
        )
        
    def search_documents(self, query: str, user_id: str, k: int = 4) -> List[Document]:
        """Search for relevant documents based on the query and filtered by user_id."""
        return self.vector_store.similarity_search(
            query, k=k, filter={"user_id": user_id}
        )
```

## LangChain Modules in Use

- **LangChain Core**: Provides base abstractions for building applications
- **LangChain Community**: Contains integrations with various tools, databases, and LLMs
- **LangChain Postgres**: Integration with PostgreSQL for vector storage
- **OpenAI Embeddings**: Used for converting text into vector embeddings

## Resources

- [LangChain Documentation](https://python.langchain.com/docs/get_started)
- [LangChain Postgres Documentation](https://python.langchain.com/docs/integrations/vectorstores/pgvector)
- [OpenAI Embeddings](https://python.langchain.com/docs/integrations/text_embedding/openai) 