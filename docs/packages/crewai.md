# CrewAI Documentation

CrewAI is a framework for orchestrating role-playing autonomous AI agents. It's designed to facilitate collaboration between agents, enabling them to work together to accomplish complex tasks.

## Key Features

- **Agent Roles**: Define specialized agents with different roles and responsibilities
- **Tasks**: Create structured tasks for agents to complete
- **Process Management**: Control how agents collaborate and share information
- **Tool Integration**: Connect agents with external tools and APIs
- **Memory Integration**: Maintain context across multiple interactions

## Potential Usage in SoloCircuit

CrewAI could be integrated into SoloCircuit to create a more sophisticated document processing pipeline:

```python
from crewai import Agent, Task, Crew, Process
from langchain_openai import ChatOpenAI
from app.services.postgres_vector_store import PostgresVectorStoreService

# Set up the vector store
vector_store = PostgresVectorStoreService()

# Create specialized agents
researcher = Agent(
    role="Legal Researcher",
    goal="Find relevant legal information in documents",
    backstory="You are an expert legal researcher with years of experience in legal document analysis.",
    verbose=True,
    llm=ChatOpenAI(model="gpt-4"),
    tools=[vector_store.search_documents]
)

summarizer = Agent(
    role="Document Summarizer",
    goal="Create concise summaries of legal documents",
    backstory="You specialize in distilling complex legal documents into clear summaries.",
    verbose=True,
    llm=ChatOpenAI(model="gpt-4"),
)

# Define tasks
research_task = Task(
    description="Search the database for documents relevant to the user's query",
    agent=researcher,
    expected_output="A list of relevant document excerpts"
)

summarize_task = Task(
    description="Create a comprehensive summary of the research findings",
    agent=summarizer,
    expected_output="A concise summary of the information found",
    context=[research_task]
)

# Create the crew
legal_crew = Crew(
    agents=[researcher, summarizer],
    tasks=[research_task, summarize_task],
    process=Process.sequential,
    verbose=True
)

# Execute the crew
result = legal_crew.kickoff(inputs={"query": "What are my legal obligations?", "user_id": "user123"})
```

## Resources

- [CrewAI Documentation](https://docs.crewai.com/)
- [CrewAI GitHub](https://github.com/crewai/crewai)
- [CrewAI Cookbook](https://github.com/crewai/crewAI-cookbook) 