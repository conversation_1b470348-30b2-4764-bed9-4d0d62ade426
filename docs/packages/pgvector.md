# pgvector Documentation

pgvector is a PostgreSQL extension for vector similarity search. It provides vector data types and operators for working with embeddings in a PostgreSQL database.

## Key Features

- **Vector Data Type**: Adds a vector data type to PostgreSQL
- **Similarity Search**: Supports multiple distance metrics (L2, inner product, cosine)
- **Indexing**: Provides HNSW and IVFFlat indexes for fast approximate search
- **Integration**: Works with popular Python libraries like LangChain and SQLAlchemy

## Usage in SoloCircuit

In SoloCircuit, pgvector is used as the backend for our vector database via the LangChain PGVector integration. This allows us to store document embeddings and perform similarity search efficiently:

```python
# Database schema (automatically created by LangChain PGVector)
# 
# CREATE TABLE IF NOT EXISTS langchain_pg_embedding (
#     id UUID PRIMARY KEY,
#     embedding VECTOR(1536),
#     document TEXT,
#     cmetadata JSONB,
#     custom_id TEXT
# );
#
# CREATE INDEX IF NOT EXISTS langchain_pg_embedding_idx ON langchain_pg_embedding 
# USING hnsw (embedding vector_cosine_ops) WITH (m=16, ef_construction=64);

from langchain_postgres import PGVector
from langchain_openai import OpenAIEmbeddings

# Create a PGVector instance for document storage
vector_store = PGVector(
    collection_name="document_embeddings",
    connection_string="postgresql://user:pass@localhost:5432/vectordb",
    embedding_function=OpenAIEmbeddings(),
    use_existing_collection=True,
)

# Search for similar documents
results = vector_store.similarity_search(
    "example query", 
    k=5,  # number of results
    filter={"user_id": "user123"}  # metadata filter
)
```

## PostgreSQL Setup for pgvector

To use pgvector in a PostgreSQL database, the extension must be installed:

```sql
-- Install the extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Example query using vector operations
SELECT * FROM langchain_pg_embedding
ORDER BY embedding <-> '[0.1, 0.2, ...]'::vector
LIMIT 5;
```

## Resources

- [pgvector GitHub](https://github.com/pgvector/pgvector)
- [pgvector Documentation](https://github.com/pgvector/pgvector/blob/master/README.md)
- [LangChain PGVector Documentation](https://python.langchain.com/docs/integrations/vectorstores/pgvector) 