# HTTP Keepalive Support in SoloCircuit

This document explains how to use HTTP keepalive in the SoloCircuit Ruby client to prevent timeout issues.

## Overview

HTTP keepalive allows a single TCP connection to be reused for multiple HTTP requests, which can:
- Reduce latency by eliminating the need to establish a new connection for each request
- Prevent timeouts when working with services that have connection limits (like Heroku)
- Improve overall performance by reducing overhead

## Configuration in the Ruby Client

The SoloCircuit Ruby client supports HTTP keepalive through the following configuration options:

```ruby
client = SoloCircuit::Client.new(
  # API credentials
  api_url: 'https://your-solocircuit-api.com',
  token: 'your-jwt-token',
  
  # Keepalive configuration
  keepalive: true,  # Enable/disable keepalive (default: true)
  keepalive_options: {
    timeout: 65,    # Keepalive timeout in seconds (default: 60)
    max: 100        # Maximum requests per connection (default: 100)
  }
)
```

### Configuration Options

- `keepalive` (Boolean): Enable or disable HTTP keepalive (default: true)
- `keepalive_options` (Hash):
  - `timeout` (Integer): How long to keep a connection alive in seconds
  - `max` (Integer): Maximum number of requests to send over a single connection

## Best Practices

1. **Match Server Configuration**: Set your timeout to match the server's keepalive timeout (65 seconds for SoloCircuit API)
2. **Connection Pooling**: For high-volume applications, consider using a connection pool with Faraday
3. **Error Handling**: Always implement proper error handling for network-related issues

## Example Usage in Rails

In a Rails application, initialize the client once in an initializer:

```ruby
# config/initializers/solocircuit.rb
SOLOCIRCUIT_CLIENT = SoloCircuit::Client.new(
  api_url: ENV['SOLOCIRCUIT_API_URL'],
  token: ENV['SOLOCIRCUIT_API_TOKEN'],
  keepalive: true,
  keepalive_options: { timeout: 65, max: 100 }
)
```

Then use the shared instance throughout your application.

## Troubleshooting

If you're still experiencing timeout issues:

1. Verify your server's keepalive configuration
2. Check if your environment (like Heroku) has specific timeout settings
3. Consider increasing the timeout value if needed
4. Look for any proxies or load balancers that might be closing idle connections 