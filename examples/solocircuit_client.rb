require 'faraday'
require 'faraday/multipart'
require 'json'

module SoloCircuit
  class Client
    attr_reader :api_url, :token

    # Initialize the client with configuration
    # @param api_url [String] The URL of the SoloCircuit API
    # @param token [String] The JWT token for authentication
    # @param keepalive [Boolean] Whether to use HTTP keepalive (default: true)
    # @param keepalive_options [Hash] Options for HTTP keepalive
    def initialize(api_url: ENV['SOLOCIRCUIT_API_URL'], 
                   token: ENV['SOLOCIRCUIT_API_TOKEN'],
                   keepalive: true,
                   keepalive_options: { timeout: 60, max: 100 })
      @api_url = api_url || 'http://localhost:8000'
      @token = token
      @keepalive = keepalive
      @keepalive_options = keepalive_options
      
      raise ArgumentError, 'API token is required' unless @token
    end

    # Upload a document to SoloCircuit
    # @param user_id [String] The user ID to associate with the document
    # @param file [File] The file to upload (optional)
    # @param text [String] Text content to upload (optional)
    # @return [Hash] The response from the API
    def upload_document(user_id:, file: nil, text: nil)
      raise ArgumentError, 'Either file or text must be provided' if file.nil? && text.nil?
      
      endpoint = "/api/documents?user_id=#{user_id}"
      
      if file
        # Upload a file
        body = { 
          file: Faraday::UploadIO.new(file, mime_type(file), File.basename(file)),
          user_id: user_id
        }
        post(endpoint, body: body)
      else
        # Upload text content
        json_data = { text: text, user_id: user_id }.to_json
        post(endpoint, body: { json_data: json_data })
      end
    end
    
    # Query documents with a natural language question
    # @param user_id [String] The user ID to query documents for
    # @param query [String] The question to ask
    # @param provider [String] Optional LLM provider to use
    # @param model [String] Optional LLM model to use
    # @return [Hash] The response from the API
    def query_documents(user_id:, query:, provider: nil, model: nil)
      endpoint = build_query_endpoint("/api/query", provider: provider, model: model)
      post(endpoint, json: { query: query, user_id: user_id })
    end
    
    # Delete a document by ID
    # @param document_id [String] The document ID to delete
    # @param user_id [String] The user ID that owns the document
    # @return [Hash] The response from the API
    def delete_document(document_id:, user_id:)
      delete("/api/documents", json: { document_id: document_id, user_id: user_id })
    end
    
    # Get document count for a user
    # @param user_id [String] The user ID to get document count for
    # @return [Hash] The response from the API
    def document_count(user_id:)
      get("/api/documents/count?user_id=#{user_id}")
    end
    
    # Extract structured lawsuit data
    # @param user_id [String] The user ID to extract lawsuit data for
    # @param config [Hash] Optional configuration for the extraction
    # @return [Hash] The response from the API
    def extract_lawsuit_data(user_id:, config: {})
      post("/api/extract-lawsuit-data", json: { user_id: user_id, config: config })
    end
    
    private
    
    # Create a Faraday connection with authentication
    # @return [Faraday::Connection] The connection object
    def connection
      @connection ||= Faraday.new(url: api_url) do |conn|
        conn.request :multipart
        conn.request :url_encoded
        conn.headers['Authorization'] = "Bearer #{token}"
        
        # Add keepalive headers if enabled
        if @keepalive
          conn.headers['Connection'] = 'keep-alive'
          conn.headers['Keep-Alive'] = "timeout=#{@keepalive_options[:timeout]}, max=#{@keepalive_options[:max]}"
        end
        
        # Configure connection options for keepalive
        conn.options.timeout = @keepalive_options[:timeout] if @keepalive
        conn.options.open_timeout = 30 # Timeout for opening the connection
        
        conn.adapter Faraday.default_adapter
      end
    end
    
    # Make a GET request
    # @param endpoint [String] The API endpoint
    # @param headers [Hash] Optional additional headers
    # @return [Hash] The parsed response
    def get(endpoint, headers: {})
      response = connection.get(endpoint, nil, headers)
      handle_response(response)
    end
    
    # Make a POST request
    # @param endpoint [String] The API endpoint
    # @param body [Hash] Optional form data
    # @param json [Hash] Optional JSON data
    # @param headers [Hash] Optional additional headers
    # @return [Hash] The parsed response
    def post(endpoint, body: nil, json: nil, headers: {})
      response = if json
        connection.post(endpoint) do |req|
          req.headers['Content-Type'] = 'application/json'
          req.headers.merge!(headers)
          req.body = json.to_json
        end
      else
        connection.post(endpoint, body, headers)
      end
      
      handle_response(response)
    end
    
    # Make a DELETE request
    # @param endpoint [String] The API endpoint
    # @param json [Hash] Optional JSON data
    # @param headers [Hash] Optional additional headers
    # @return [Hash] The parsed response
    def delete(endpoint, json: nil, headers: {})
      response = if json
        connection.delete do |req|
          req.url endpoint
          req.headers['Content-Type'] = 'application/json'
          req.headers.merge!(headers)
          req.body = json.to_json
        end
      else
        connection.delete(endpoint, nil, headers)
      end
      handle_response(response)
    end
    
    # Build an endpoint URL with query parameters
    # @param base [String] The base endpoint
    # @param provider [String] Optional LLM provider
    # @param model [String] Optional LLM model
    # @return [String] The endpoint with query parameters
    def build_query_endpoint(base, provider: nil, model: nil)
      return base if provider.nil? && model.nil?
      
      params = []
      params << "llm_provider=#{provider}" if provider
      params << "llm_model=#{model}" if model
      
      "#{base}?#{params.join('&')}"
    end
    
    # Handle API responses
    # @param response [Faraday::Response] The response from the API
    # @return [Hash] The parsed response body
    def handle_response(response)
      if response.success?
        JSON.parse(response.body)
      else
        error = begin
          JSON.parse(response.body)
        rescue JSON::ParserError
          { detail: response.body.to_s }
        end
        
        raise ApiError.new(error, response.status)
      end
    end
    
    # Determine the MIME type of a file
    # @param file [File] The file to check
    # @return [String] The MIME type
    def mime_type(file)
      case File.extname(file.path).downcase
      when '.pdf' then 'application/pdf'
      when '.docx' then 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      when '.txt' then 'text/plain'
      when '.jpg', '.jpeg' then 'image/jpeg'
      when '.png' then 'image/png'
      else 'application/octet-stream'
      end
    end
  end
  
  # Custom error class for API errors
  class ApiError < StandardError
    attr_reader :error, :status
    
    def initialize(error, status)
      @error = error
      @status = status
      super(error_message)
    end
    
    def error_message
      detail = error.is_a?(Hash) ? error['detail'] : error.to_s
      "SoloCircuit API Error (#{status}): #{detail}"
    end
  end
end 