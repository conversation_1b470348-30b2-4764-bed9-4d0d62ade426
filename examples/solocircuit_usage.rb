require_relative 'solocircuit_client'

# Example of using the SoloCircuit client in a Rails application

# In config/initializers/solocircuit.rb
# Initialize the client once
SOLOCIRCUIT_CLIENT = SoloCircuit::Client.new(
  # These values are read from environment variables by default
  # api_url: ENV['SOLOCIRCUIT_API_URL'],
  # token: ENV['SOLOCIRCUIT_API_TOKEN'],
  
  # Configure HTTP keepalive to prevent connection timeouts
  keepalive: true,
  keepalive_options: {
    timeout: 65,  # Match the server-side timeout (65 seconds)
    max: 100      # Maximum number of requests per connection
  }
)

# Example usage in a Rails controller
class DocumentsController < ApplicationController
  def upload
    user_id = current_user.id
    file = params[:file]
    
    begin
      # Upload the document
      result = SOLOCIRCUIT_CLIENT.upload_document(
        user_id: user_id,
        file: file.tempfile
      )
      
      # Store the document ID in your database if needed
      Document.create(
        user_id: user_id,
        solocircuit_document_id: result['document_id'],
        filename: file.original_filename
      )
      
      redirect_to documents_path, notice: 'Document uploaded successfully'
    rescue SoloCircuit::ApiError => e
      redirect_to new_document_path, alert: e.message
    end
  end
  
  def search
    user_id = current_user.id
    query = params[:query]
    
    begin
      # Query the documents
      result = SOLOCIRCUIT_CLIENT.query_documents(
        user_id: user_id,
        query: query
      )
      
      # Display the answer to the user
      @answer = result['answer']
      @sources = result['sources']
      
      render :search_results
    rescue SoloCircuit::ApiError => e
      redirect_to search_path, alert: e.message
    end
  end
  
  def destroy
    document = Document.find(params[:id])
    
    begin
      # Delete the document from SoloCircuit
      SOLOCIRCUIT_CLIENT.delete_document(
        document_id: document.solocircuit_document_id,
        user_id: current_user.id
      )
      
      # Delete from your database
      document.destroy
      
      redirect_to documents_path, notice: 'Document deleted successfully'
    rescue SoloCircuit::ApiError => e
      redirect_to documents_path, alert: e.message
    end
  end
end

# Example of using text content instead of a file
class NotesController < ApplicationController
  def create
    user_id = current_user.id
    text = params[:content]
    
    begin
      # Upload the text as a document
      result = SOLOCIRCUIT_CLIENT.upload_document(
        user_id: user_id,
        text: text
      )
      
      redirect_to notes_path, notice: 'Note saved successfully'
    rescue SoloCircuit::ApiError => e
      redirect_to new_note_path, alert: e.message
    end
  end
end

# Example of extracting lawsuit data
class LawsuitsController < ApplicationController
  def extract_data
    user_id = current_user.id
    
    begin
      # Extract lawsuit data from documents
      result = SOLOCIRCUIT_CLIENT.extract_lawsuit_data(
        user_id: user_id,
        config: { 
          model: params[:llm_provider],
          model_name: params[:llm_model]
        }
      )
      
      # Process the extracted data
      @lawsuit_data = result
      
      render :extracted_data
    rescue SoloCircuit::ApiError => e
      redirect_to lawsuits_path, alert: e.message
    end
  end
end 