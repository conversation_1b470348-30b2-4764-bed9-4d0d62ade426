# Database Management

This document outlines the database structure and management scripts for the SoloCircuit project.

## Database Structure

The application uses PostgreSQL databases with the following naming convention based on the environment:

- Development/Testing: `solocircuit_test`
- Staging: `solocircuit_staging`
- Production: `solocircuit_prod`

## Key Tables

- `llm_traces`: Stores information about LLM interactions, including prompts, responses, and metadata

## Database Management Scripts

### Database Migration

The `database_migration.py` script handles database migrations for all environments. It automatically:

1. Determines the correct database name based on the environment
2. Creates the database if needed (with appropriate flags)
3. Creates or updates tables 

#### Usage

```bash
# Basic usage - apply migrations to existing database
python database_migration.py

# Create database if it doesn't exist, then apply migrations
python database_migration.py --create-db

# Force operations (useful for development)
python database_migration.py --create-db --force
```

### Deployment Helper

The `deploy_db.sh` script simplifies database deployment for different environments:

```bash
# Deploy in development (default)
./deploy_db.sh

# Deploy in staging
ENVIRONMENT=staging ./deploy_db.sh

# Deploy in production
ENVIRONMENT=production ./deploy_db.sh
```

### Test Setup

To quickly set up a test database:

```bash
./setup_test_db.sh
```

## Database Connection

The database connection URL should be provided as an environment variable:

```
POSTGRES_URL="postgresql+asyncpg://username:password@host:port/database"
```

For tests, you can specify:

```
TEST_POSTGRES_URL="postgresql+asyncpg://username:password@host:port/solocircuit_test"
```

## Environment Variables

- `ENVIRONMENT`: Set to "development", "staging", or "production" to determine the target database
- `POSTGRES_URL`: The base PostgreSQL connection URL
- `TEST_POSTGRES_URL`: Optional specific URL for tests 